<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="186bb0ad-00b0-4395-9052-b8cd3acfe946" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="D:\workspace\IDEA\mc\ikun">
          <activation />
        </task>
        <task path="$PROJECT_DIR$">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand />
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="InvalidFacetManager">
    <ignored-facets>
      <facet id="ikun.main/invalid/Minecraft" />
      <facet id="ikun.test/invalid/Minecraft" />
    </ignored-facets>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2WiDLdhJzW07Ju5f0rAarIfm7Mh" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Gradle.D:/MyDate/Code/IDEA/ikun [:createMcpToSrg :createMcpToSrg :createMcpToSrg].executor": "Run",
    "Gradle.ikun [:createMcpToSrg :createMcpToSrg :createMcpToSrg].executor": "Run",
    "Gradle.ikun [runClient].executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "reference.settingsdialog.project.gradle",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\workspace\IDEA\mc\ikun\src\main\resources\assets\ikun\textures\block" />
      <recent name="D:\workspace\IDEA\mc\ikun\src\main\resources\assets\ikun\textures\item" />
      <recent name="D:\workspace\IDEA\mc\ikun\src\main\resources\assets\ikun\textures" />
      <recent name="D:\workspace\IDEA\mc\ikun\src\main\resources\data\minecraft\tags" />
      <recent name="D:\workspace\IDEA\mc\ikun\src\main\java\com\yx\ikun" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\workspace\IDEA\mc\ikun\src\main\resources\assets\ikun\textures\item" />
      <recent name="D:\workspace\IDEA\mc\ikun\src\main\resources\assets\ikun\textures\block" />
      <recent name="D:\workspace\IDEA\mc\ikun\src\main\resources\assets\ikun" />
      <recent name="D:\workspace\IDEA\mc\ikun\src\main\resources\assets\ikun\sounds" />
    </key>
  </component>
  <component name="RunManager" selected="Gradle.ikun [runClient]">
    <configuration name="ikun [build]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="build" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="ikun [clean]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="clean" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="ikun [runClient]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="runClient" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration name="ikun build" type="GradleRunConfiguration" factoryName="Gradle">
      <ExternalSystemSettings>
        <option name="executionName" value="ikun build" />
        <option name="externalProjectPath" value="D:\workspace\IDEA\mc\ikun" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="build" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Gradle.ikun [runClient]" />
        <item itemvalue="Gradle.ikun [build]" />
        <item itemvalue="Gradle.ikun [clean]" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fdfe4dae3a2d-intellij.indexing.shared.core-IU-243.22562.218" />
        <option value="bundled-js-predefined-d6986cc7102b-deb605915726-JavaScript-IU-243.22562.218" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="186bb0ad-00b0-4395-9052-b8cd3acfe946" name="更改" comment="" />
      <created>1697206453345</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1697206453345</updated>
      <workItem from="1697206454490" duration="5537000" />
      <workItem from="1697246772727" duration="8118000" />
      <workItem from="1697279349425" duration="5335000" />
      <workItem from="1697286211274" duration="6869000" />
      <workItem from="1697334555981" duration="1758000" />
      <workItem from="1697339225199" duration="1228000" />
      <workItem from="1697346739718" duration="648000" />
      <workItem from="1697356435159" duration="607000" />
      <workItem from="1697381820221" duration="2476000" />
      <workItem from="1697445280552" duration="7205000" />
      <workItem from="1697462163527" duration="5010000" />
      <workItem from="1697540217572" duration="12419000" />
      <workItem from="1697635149024" duration="716000" />
      <workItem from="1697635898857" duration="3908000" />
      <workItem from="1697686242034" duration="451000" />
      <workItem from="1721225592425" duration="406000" />
      <workItem from="1721226036597" duration="43000" />
      <workItem from="1721226232085" duration="347000" />
      <workItem from="1721226592024" duration="82000" />
      <workItem from="1721226780435" duration="151000" />
      <workItem from="1721226939624" duration="241000" />
      <workItem from="1721227232738" duration="942000" />
      <workItem from="1721441012045" duration="24000" />
      <workItem from="1753867054762" duration="109000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>
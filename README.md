# 🐔 IKUN Mod

<div align="center">

![Minecraft](https://img.shields.io/badge/Minecraft-1.19-green?style=for-the-badge&logo=minecraft)
![Forge](https://img.shields.io/badge/Forge-41.1.0-orange?style=for-the-badge)
![Java](https://img.shields.io/badge/Java-17-blue?style=for-the-badge&logo=java)
![License](https://img.shields.io/badge/License-All%20Rights%20Reserved-red?style=for-the-badge)

*一个充满趣味的 Minecraft 模组，为你的世界带来全新的 IKUN 体验！*

</div>

## 📖 简介

IKUN Mod 是一个为 Minecraft 1.19 设计的 Forge 模组，添加了丰富的游戏内容，包括新的物品、方块、音乐和装备系统。无论你是想要新的冒险体验，还是想要收集独特的物品，这个模组都能满足你的需求。

## ✨ 特性

### 🍳 食物系统
- **煎蛋** - 美味的食物，恢复饥饿值
- **生 IKUN** - 可以烹饪的原材料
- **IKUN** - 烹饪后的美味食物

### ⚔️ 装备系统
- **IKUN 工具套装**
  - IKUN 剑 - 强力的近战武器
  - IKUN 镐 - 高效的挖掘工具
  - IKUN 斧 - 快速砍伐工具
  - IKUN 铲 - 挖掘土壤的利器
  - IKUN 锄 - 农业必备工具

- **IKUN 护甲套装**
  - IKUN 头盔 - 保护你的头部
  - IKUN 胸甲 - 强化防护
  - IKUN 护腿 - 腿部防护
  - IKUN 靴子 - 足部保护

### 🏗️ 方块系统
- **IKUN 矿石** - 在主世界中生成的珍贵矿石
- **深层 IKUN 矿石** - 在深层岩石中发现的稀有矿石
- **IKUN 方块** - 由 IKUN 制成的装饰方块
- **冬日窗户** - 美丽的透明装饰方块
- **电梯方块** - 特殊功能方块

### 🎵 音乐系统
- **5 张音乐唱片** - 独特的 IKUN 主题音乐
- **特殊音效** - 包括 "ikun_ngm" 等趣味音效

### 🔧 实用工具
- **矿物探测器** - 帮助你找到珍贵的矿物
- **IKUN 煤炭** - 特殊的燃料物品
- **魔法粉尘** - 神秘的材料
- **IKUN 粒** - 制作材料

### 🎨 装饰系统
- **自定义绘画** - 独特的艺术作品装饰你的世界

## 🚀 安装指南

### 前置要求
- Minecraft 1.19
- Minecraft Forge 41.1.0 或更高版本
- Java 17

### 安装步骤
1. 确保已安装 Minecraft Forge 1.19
2. 下载最新版本的 IKUN Mod
3. 将 `.jar` 文件放入 `mods` 文件夹
4. 启动游戏并享受！

## 🛠️ 开发

### 构建项目
```bash
# 克隆项目
git clone <repository-url>
cd ikun

# 构建模组
./gradlew build

# 运行客户端测试
./gradlew runClient
```

### 开发环境
- **IDE**: IntelliJ IDEA 推荐
- **Java**: OpenJDK 17
- **Gradle**: 自动管理依赖

## 📁 项目结构

```
ikun/
├── src/main/java/com/yx/ikun/
│   ├── Ikun.java                 # 主模组类
│   ├── item/                     # 物品相关
│   │   ├── ModItem.java         # 物品注册
│   │   ├── ModCreativeModeTab.java # 创造模式标签页
│   │   ├── ModFood.java         # 食物属性
│   │   ├── ModTiers.java        # 工具等级
│   │   └── custom/              # 自定义物品
│   ├── block/                   # 方块相关
│   │   ├── ModBlock.java        # 方块注册
│   │   └── custom/              # 自定义方块
│   ├── sound/                   # 音效相关
│   │   └── ModSound.java        # 音效注册
│   ├── painting/                # 绘画相关
│   │   └── ModPaintings.java    # 绘画注册
│   └── world/                   # 世界生成
│       └── feature/             # 特性生成
└── src/main/resources/
    ├── assets/ikun/             # 资源文件
    │   ├── textures/           # 材质贴图
    │   ├── models/             # 模型文件
    │   ├── sounds/             # 音效文件
    │   └── lang/               # 语言文件
    └── data/                   # 数据文件
```

## 🎮 游戏内容

### 创造模式标签页
所有 IKUN Mod 的物品都可以在专门的 "IKUN" 创造模式标签页中找到，方便你快速获取和使用。

### 特殊功能
- **矿物探测器**: 使用时会发出声音提示附近是否有矿物
- **电梯方块**: 提供特殊的传送功能
- **IKUN 护甲**: 穿戴时可能提供特殊效果

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

All Rights Reserved - 保留所有权利

## 👨‍💻 作者

- **开发者**: qishuiqishui
- **包名**: com.yx

---

<div align="center">

**享受你的 IKUN 冒险之旅！** 🐔✨

</div>

<div align="center">

# 🐔 IKUN Mod

![IKUN Mod 展示](./images/1.png)

*一个充满趣味的 Minecraft 模组，为你的世界带来全新的 IKUN 体验！*

---

[![Minecraft](https://img.shields.io/badge/Minecraft-1.19-brightgreen?style=for-the-badge&logo=minecraft&logoColor=white)](https://minecraft.net)
[![Forge](https://img.shields.io/badge/Forge-41.1.0-orange?style=for-the-badge&logo=curseforge&logoColor=white)](https://files.minecraftforge.net)
[![Java](https://img.shields.io/badge/Java-17-blue?style=for-the-badge&logo=openjdk&logoColor=white)](https://openjdk.org)
[![License](https://img.shields.io/badge/License-All%20Rights%20Reserved-red?style=for-the-badge)](./LICENSE)

![Downloads](https://img.shields.io/badge/Downloads-0-blue?style=for-the-badge&logo=download)
![GitHub Stars](https://img.shields.io/github/stars/SodaSizzle/forge1.19-ikun-mod?style=for-the-badge&logo=github)
![GitHub Issues](https://img.shields.io/github/issues/SodaSizzle/forge1.19-ikun-mod?style=for-the-badge&logo=github)
![GitHub Forks](https://img.shields.io/github/forks/SodaSizzle/forge1.19-ikun-mod?style=for-the-badge&logo=github)

[📥 下载](#-安装指南) • [📖 文档](#-特性) • [� 开发](#-开发指南)

</div>

---

## 📖 简介

<div align="center">
  <img src="./images/2.png" alt="IKUN Mod 游戏截图" width="600">
</div>

IKUN Mod 是一个为 Minecraft 1.19 设计的 Forge 模组，添加了丰富的游戏内容，包括新的物品、方块、音乐和装备系统。无论你是想要新的冒险体验，还是想要收集独特的物品，这个模组都能满足你的需求。

### 🌟 为什么选择 IKUN Mod？

- 🎮 **丰富的游戏内容** - 超过 20+ 新物品和方块
- 🎵 **独特的音乐体验** - 5 张原创音乐唱片
- ⚔️ **完整的装备系统** - 从工具到护甲一应俱全
- 🏗️ **创新的方块机制** - 电梯方块等特殊功能
- 🎨 **精美的视觉效果** - 自定义材质和模型

## ✨ 特性

<details>
<summary>🍳 <strong>食物系统</strong></summary>

| 物品 | 描述 | 效果 |
|------|------|------|
| 🍳 **煎蛋** | 美味的食物 | 恢复 6 点饥饿值 |
| 🥩 **生 IKUN** | 可以烹饪的原材料 | 可烹饪成 IKUN |
| 🍖 **IKUN** | 烹饪后的美味食物 | 恢复更多饥饿值 |

</details>

<details>
<summary>⚔️ <strong>装备系统</strong></summary>

### 🛠️ IKUN 工具套装
| 工具 | 攻击力/效率 | 耐久度 | 特殊效果 |
|------|-------------|--------|----------|
| ⚔️ **IKUN 剑** | 8 攻击力 | 1561 | 快速攻击 |
| ⛏️ **IKUN 镐** | 效率 IV | 1561 | 快速挖掘 |
| 🪓 **IKUN 斧** | 10 攻击力 | 1561 | 快速砍伐 |
| 🥄 **IKUN 铲** | 6 攻击力 | 1561 | 快速挖掘 |
| 🔨 **IKUN 锄** | 农业专用 | 1561 | 高效耕作 |

### 🛡️ IKUN 护甲套装
| 护甲 | 防护值 | 韧性 | 特殊效果 |
|------|---------|------|----------|
| ⛑️ **IKUN 头盔** | 3 点防护 | 2.0 | 夜视效果 |
| 🦺 **IKUN 胸甲** | 8 点防护 | 2.0 | 力量提升 |
| 👖 **IKUN 护腿** | 6 点防护 | 2.0 | 速度提升 |
| 👢 **IKUN 靴子** | 3 点防护 | 2.0 | 跳跃提升 |

</details>

<details>
<summary>🏗️ <strong>方块系统</strong></summary>

| 方块 | 硬度 | 工具 | 掉落物 | 特殊功能 |
|------|------|------|--------|----------|
| 💎 **IKUN 矿石** | 5.0 | 铁镐+ | IKUN 原料 + 经验 | 主世界生成 |
| 🗿 **深层 IKUN 矿石** | 6.0 | 铁镐+ | IKUN 原料 + 更多经验 | 深层生成 |
| 🧱 **IKUN 方块** | 6.0 | 铁镐+ | 自身 | 装饰方块 |
| 🪟 **冬日窗户** | 0.3 | 任意 | 自身 | 透明装饰 |
| 🔼 **电梯方块** | 3.0 | 镐 | 自身 | 传送功能 |

</details>

<details>
<summary>🎵 <strong>音乐系统</strong></summary>

### � 音乐唱片收藏
| 唱片 | 时长 | 风格 | 获取方式 |
|------|------|------|----------|
| 🎵 **IKUN Music One** | 3:24 | 轻快 | 合成获得 |
| 🎵 **IKUN Music Two** | 2:56 | 抒情 | 合成获得 |
| 🎵 **IKUN Music Three** | 4:12 | 史诗 | 合成获得 |
| 🎵 **IKUN Music Four** | 3:45 | 神秘 | 合成获得 |
| 🎵 **IKUN Music Five** | 2:33 | 欢快 | 合成获得 |

### 🔊 特殊音效
- 🎯 **ikun_ngm** - 趣味音效
- 🔍 **矿物探测音效** - 发现矿物时播放

</details>

<details>
<summary>🔧 <strong>实用工具</strong></summary>

| 工具 | 功能 | 耐久度 | 使用方法 |
|------|------|--------|----------|
| 🔍 **矿物探测器** | 探测附近矿物 | 16 | 右键使用 |
| ⚫ **IKUN 煤炭** | 高效燃料 | - | 燃烧时间更长 |
| ✨ **魔法粉尘** | 合成材料 | - | 用于高级合成 |
| 🔸 **IKUN 粒** | 基础材料 | - | 制作 IKUN 物品 |

</details>

<details>
<summary>🎨 <strong>装饰系统</strong></summary>

### 🖼️ 自定义绘画
- 🎨 **IKUN 主题画作** - 独特的艺术作品
- 🏠 **装饰你的世界** - 让建筑更加生动

</details>

## 🚀 安装指南

<div align="center">

### 📋 前置要求

| 组件 | 版本要求 | 下载链接 |
|------|----------|----------|
| ![Minecraft](https://img.shields.io/badge/Minecraft-1.19-brightgreen?style=flat-square&logo=minecraft) | 1.19 | [官方网站](https://minecraft.net) |
| ![Forge](https://img.shields.io/badge/Forge-41.1.0+-orange?style=flat-square&logo=curseforge) | 41.1.0+ | [Forge 官网](https://files.minecraftforge.net) |
| ![Java](https://img.shields.io/badge/Java-17-blue?style=flat-square&logo=openjdk) | 17 | [OpenJDK](https://openjdk.org) |

</div>

### 📥 安装步骤

1. **安装 Minecraft Forge**
   ```bash
   # 下载 Forge 1.19-41.1.0 安装器
   # 运行安装器并选择 "Install client"
   ```

2. **下载 IKUN Mod**
   - 下载最新版本的 `.jar` 文件

3. **安装模组**
   ```bash
   # Windows
   %appdata%\.minecraft\mods\

   # macOS
   ~/Library/Application Support/minecraft/mods/

   # Linux
   ~/.minecraft/mods/
   ```

4. **启动游戏**
   - 选择 Forge 1.19 配置文件
   - 启动游戏并享受！

> ⚠️ **注意**: 请确保下载的模组版本与你的 Minecraft 和 Forge 版本兼容！

## 🛠️ 开发

### 构建项目
```bash
# 克隆项目
git clone <repository-url>
cd ikun

# 构建模组
117.72.189.98

# 运行客户端测试
./gradlew runClient
```

### 开发环境
- **IDE**: IntelliJ IDEA 推荐
- **Java**: OpenJDK 17
- **Gradle**: 自动管理依赖

## 📁 项目结构

```
ikun/
├── src/main/java/com/yx/ikun/
│   ├── Ikun.java                 # 主模组类
│   ├── item/                     # 物品相关
│   │   ├── ModItem.java         # 物品注册
│   │   ├── ModCreativeModeTab.java # 创造模式标签页
│   │   ├── ModFood.java         # 食物属性
│   │   ├── ModTiers.java        # 工具等级
│   │   └── custom/              # 自定义物品
│   ├── block/                   # 方块相关
│   │   ├── ModBlock.java        # 方块注册
│   │   └── custom/              # 自定义方块
│   ├── sound/                   # 音效相关
│   │   └── ModSound.java        # 音效注册
│   ├── painting/                # 绘画相关
│   │   └── ModPaintings.java    # 绘画注册
│   └── world/                   # 世界生成
│       └── feature/             # 特性生成
└── src/main/resources/
    ├── assets/ikun/             # 资源文件
    │   ├── textures/           # 材质贴图
    │   ├── models/             # 模型文件
    │   ├── sounds/             # 音效文件
    │   └── lang/               # 语言文件
    └── data/                   # 数据文件
```

## 🎮 游戏内容

### 创造模式标签页
所有 IKUN Mod 的物品都可以在专门的 "IKUN" 创造模式标签页中找到，方便你快速获取和使用。

### 特殊功能
- **矿物探测器**: 使用时会发出声音提示附近是否有矿物
- **电梯方块**: 提供特殊的传送功能
- **IKUN 护甲**: 穿戴时可能提供特殊效果

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

All Rights Reserved - 保留所有权利

## 🤝 贡献

我们欢迎所有形式的贡献！无论是报告 bug、提出新功能建议，还是提交代码改进。

<div align="center">

![Contributors](https://img.shields.io/github/contributors/SodaSizzle/forge1.19-ikun-mod?style=for-the-badge)
![Pull Requests](https://img.shields.io/github/issues-pr/SodaSizzle/forge1.19-ikun-mod?style=for-the-badge)
![Last Commit](https://img.shields.io/github/last-commit/SodaSizzle/forge1.19-ikun-mod?style=for-the-badge)

</div>

### � 开发指南

<details>
<summary><strong>本地开发环境设置</strong></summary>

```bash
# 克隆仓库（如果你有 GitHub 仓库的话）
git clone <your-repository-url>
cd forge1.19-ikun-mod

# 设置开发环境
./gradlew genEclipseRuns
./gradlew genIntellijRuns

# 构建模组
./gradlew build

# 运行客户端测试
./gradlew runClient

# 运行服务端测试
./gradlew runServer
```

</details>

### 📝 提交指南

1. **Fork** 这个仓库
2. **创建** 你的功能分支 (`git checkout -b feature/AmazingFeature`)
3. **提交** 你的更改 (`git commit -m 'Add some AmazingFeature'`)
4. **推送** 到分支 (`git push origin feature/AmazingFeature`)
5. **打开** 一个 Pull Request

## 📄 许可证

本项目采用 **All Rights Reserved** 许可证 - 查看 [LICENSE](./LICENSE) 文件了解详情。

## �👨‍💻 作者与致谢

<div align="center">

### 🎯 核心开发者

<table>
  <tr>
    <td align="center">
      <img src="https://github.com/SodaSizzle.png" width="100px;" alt="SodaSizzle"/>
      <br />
      <sub><b>SodaSizzle</b></sub>
      <br />
      <sub>💻 主要开发者</sub>
    </td>
  </tr>
</table>

### 🙏 特别感谢

- **Minecraft Forge 团队** - 提供了强大的模组开发框架
- **Mojang Studios** - 创造了 Minecraft 这个伟大的游戏
- **社区贡献者** - 感谢所有提供反馈和建议的玩家

</div>

## 📊 项目统计

<div align="center">

![GitHub repo size](https://img.shields.io/github/repo-size/SodaSizzle/forge1.19-ikun-mod?style=for-the-badge)
![Lines of code](https://img.shields.io/tokei/lines/github/SodaSizzle/forge1.19-ikun-mod?style=for-the-badge)
![GitHub language count](https://img.shields.io/github/languages/count/SodaSizzle/forge1.19-ikun-mod?style=for-the-badge)
![GitHub top language](https://img.shields.io/github/languages/top/SodaSizzle/forge1.19-ikun-mod?style=for-the-badge)

</div>

## 🔗 相关链接

<div align="center">

![GitHub](https://img.shields.io/badge/GitHub-Repository-black?style=for-the-badge&logo=github)
![Issues](https://img.shields.io/badge/Issues-Report%20Bug-red?style=for-the-badge&logo=github)
![Minecraft](https://img.shields.io/badge/Minecraft-Mod-green?style=for-the-badge&logo=minecraft)
![Forge](https://img.shields.io/badge/Forge-Compatible-orange?style=for-the-badge&logo=curseforge)

</div>

---

<div align="center">

### 🐔 享受你的 IKUN 冒险之旅！

**如果这个项目对你有帮助，请给我们一个 ⭐ Star！**

*Made with ❤️ by [SodaSizzle](https://github.com/SodaSizzle)*

</div>

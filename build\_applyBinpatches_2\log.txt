Java Launcher: D:\Development\JDK17\bin\java.exe
Arguments: '--clean, C:\Users\<USER>\.gradle\caches\forge_gradle\mcp_repo\net\minecraft\joined\1.19-20220607.102129\joined-1.19-20220607.102129-srg.jar, --output, C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.19-41.1.0\forge-1.19-41.1.0-binpatched.jar, --apply, C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.19-41.1.0\forge-1.19-41.1.0-binpatches.lzma'
Classpath:
 - C:\Users\<USER>\.gradle\caches\forge_gradle\maven_downloader\net\minecraftforge\binarypatcher\1.1.1\binarypatcher-1.1.1-fatjar.jar
Working directory: D:\MyDate\Code\IDEA\ikun\build\_applyBinpatches_2
Main class: net.minecraftforge.binarypatcher.ConsoleTool
====================================
Applying: 
  Clean:     C:\Users\<USER>\.gradle\caches\forge_gradle\mcp_repo\net\minecraft\joined\1.19-20220607.102129\joined-1.19-20220607.102129-srg.jar
  Output:    C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.19-41.1.0\forge-1.19-41.1.0-binpatched.jar
  KeepData:  false
  Unpatched: false
  Pack200:   false
  Legacy:    false
Loading patches file: C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.19-41.1.0\forge-1.19-41.1.0-binpatches.lzma
  Reading patch com.mojang.blaze3d.pipeline.RenderTarget.binpatch
    Checksum: 863fc40a Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$BlendState.binpatch
    Checksum: e17936b0 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$BooleanState.binpatch
    Checksum: 75a02e31 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$ColorLogicState.binpatch
    Checksum: ed9c2ba9 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$ColorMask.binpatch
    Checksum: f9aad75d Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$CullState.binpatch
    Checksum: 9a7823a5 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$DepthState.binpatch
    Checksum: 8ffa2b80 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$DestFactor.binpatch
    Checksum: 4d13d59 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$LogicOp.binpatch
    Checksum: 4a89e60e Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$PolygonOffsetState.binpatch
    Checksum: d37e38a5 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$ScissorState.binpatch
    Checksum: fd6d220d Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$SourceFactor.binpatch
    Checksum: e4b3510d Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$StencilFunc.binpatch
    Checksum: d895d1d3 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$StencilState.binpatch
    Checksum: acbf38b5 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$TextureState.binpatch
    Checksum: 1adcbe6 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$Viewport.binpatch
    Checksum: 983c067b Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager.binpatch
    Checksum: b903914d Exists: true
  Reading patch com.mojang.blaze3d.platform.Window$WindowInitFailed.binpatch
    Checksum: ca66c1ad Exists: true
  Reading patch com.mojang.blaze3d.platform.Window.binpatch
    Checksum: f7f060f1 Exists: true
  Reading patch com.mojang.blaze3d.vertex.BufferBuilder$1.binpatch
    Checksum: f744123b Exists: true
  Reading patch com.mojang.blaze3d.vertex.BufferBuilder$DrawState.binpatch
    Checksum: 80f4d438 Exists: true
  Reading patch com.mojang.blaze3d.vertex.BufferBuilder$RenderedBuffer.binpatch
    Checksum: 98c0f9b5 Exists: true
  Reading patch com.mojang.blaze3d.vertex.BufferBuilder$SortState.binpatch
    Checksum: 3e8a674d Exists: true
  Reading patch com.mojang.blaze3d.vertex.BufferBuilder.binpatch
    Checksum: 7230c83c Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexConsumer.binpatch
    Checksum: 452cacc6 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormat$1.binpatch
    Checksum: 283939e7 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormat$IndexType.binpatch
    Checksum: 34a7e867 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormat$Mode.binpatch
    Checksum: addd6f22 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormat.binpatch
    Checksum: 89c7439d Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormatElement$Type.binpatch
    Checksum: 50775c2c Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormatElement$Usage$ClearState.binpatch
    Checksum: e3a9b022 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormatElement$Usage$SetupState.binpatch
    Checksum: 9861b14f Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormatElement$Usage.binpatch
    Checksum: 1ad14fd5 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormatElement.binpatch
    Checksum: 500cdc84 Exists: true
  Reading patch com.mojang.math.Matrix3f.binpatch
    Checksum: 812d1635 Exists: true
  Reading patch com.mojang.math.Matrix4f.binpatch
    Checksum: c0ac6336 Exists: true
  Reading patch com.mojang.math.Transformation.binpatch
    Checksum: 3fb311c0 Exists: true
  Reading patch com.mojang.math.Vector3f.binpatch
    Checksum: b2c5091b Exists: true
  Reading patch com.mojang.math.Vector4f.binpatch
    Checksum: 11597543 Exists: true
  Reading patch com.mojang.realmsclient.gui.screens.RealmsGenericErrorScreen.binpatch
    Checksum: 7e86c99c Exists: true
  Reading patch net.minecraft.CrashReport.binpatch
    Checksum: 259afdc1 Exists: true
  Reading patch net.minecraft.CrashReportCategory$Entry.binpatch
    Checksum: e601a895 Exists: true
  Reading patch net.minecraft.CrashReportCategory.binpatch
    Checksum: 827f126a Exists: true
  Reading patch net.minecraft.SharedConstants$1.binpatch
    Checksum: 2b0cceea Exists: true
  Reading patch net.minecraft.SharedConstants.binpatch
    Checksum: 80314ea2 Exists: true
  Reading patch net.minecraft.Util$1.binpatch
    Checksum: 1a577cf7 Exists: true
  Reading patch net.minecraft.Util$10.binpatch
    Checksum: 3146d820 Exists: true
  Reading patch net.minecraft.Util$11.binpatch
    Checksum: fdd11ae2 Exists: true
  Reading patch net.minecraft.Util$2.binpatch
    Checksum: fe3d0dc2 Exists: true
  Reading patch net.minecraft.Util$3.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.Util$4.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.Util$5.binpatch
    Checksum: 3472d359 Exists: true
  Reading patch net.minecraft.Util$6.binpatch
    Checksum: 8e5f948a Exists: true
  Reading patch net.minecraft.Util$7.binpatch
    Checksum: 4bb869c3 Exists: true
  Reading patch net.minecraft.Util$8.binpatch
    Checksum: 46cd6489 Exists: true
  Reading patch net.minecraft.Util$9.binpatch
    Checksum: b5cc484 Exists: true
  Reading patch net.minecraft.Util$IdentityStrategy.binpatch
    Checksum: ca10b43c Exists: true
  Reading patch net.minecraft.Util$OS$1.binpatch
    Checksum: 2977b230 Exists: true
  Reading patch net.minecraft.Util$OS$2.binpatch
    Checksum: ca49a517 Exists: true
  Reading patch net.minecraft.Util$OS.binpatch
    Checksum: 4a784fa2 Exists: true
  Reading patch net.minecraft.Util.binpatch
    Checksum: ead2bca9 Exists: true
  Reading patch net.minecraft.advancements.Advancement$Builder.binpatch
    Checksum: ea5a8893 Exists: true
  Reading patch net.minecraft.advancements.Advancement.binpatch
    Checksum: 3140fba6 Exists: true
  Reading patch net.minecraft.advancements.AdvancementList$Listener.binpatch
    Checksum: 87eb687a Exists: true
  Reading patch net.minecraft.advancements.AdvancementList.binpatch
    Checksum: ebb2a84a Exists: true
  Reading patch net.minecraft.advancements.AdvancementRewards$Builder.binpatch
    Checksum: 3629e580 Exists: true
  Reading patch net.minecraft.advancements.AdvancementRewards.binpatch
    Checksum: a44c201e Exists: true
  Reading patch net.minecraft.advancements.critereon.ItemPredicate$Builder.binpatch
    Checksum: d96bddb2 Exists: true
  Reading patch net.minecraft.advancements.critereon.ItemPredicate.binpatch
    Checksum: 1f058a30 Exists: true
  Reading patch net.minecraft.client.Camera$NearPlane.binpatch
    Checksum: 7d2191b4 Exists: true
  Reading patch net.minecraft.client.Camera.binpatch
    Checksum: 6c04d460 Exists: true
  Reading patch net.minecraft.client.ClientBrandRetriever.binpatch
    Checksum: e20cbb90 Exists: true
  Reading patch net.minecraft.client.ClientRecipeBook.binpatch
    Checksum: b8666582 Exists: true
  Reading patch net.minecraft.client.KeyMapping.binpatch
    Checksum: 9835b407 Exists: true
  Reading patch net.minecraft.client.KeyboardHandler$1.binpatch
    Checksum: d106fbef Exists: true
  Reading patch net.minecraft.client.KeyboardHandler.binpatch
    Checksum: 2ce71757 Exists: true
  Reading patch net.minecraft.client.Minecraft$1.binpatch
    Checksum: 7940fcf0 Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus$1.binpatch
    Checksum: fbdea71 Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus$2.binpatch
    Checksum: 23d5ea83 Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus$3.binpatch
    Checksum: dd67e9af Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus$4.binpatch
    Checksum: 3fc1e8ba Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus.binpatch
    Checksum: 65d5016 Exists: true
  Reading patch net.minecraft.client.Minecraft.binpatch
    Checksum: 8f6c2a03 Exists: true
  Reading patch net.minecraft.client.MouseHandler.binpatch
    Checksum: e4201659 Exists: true
  Reading patch net.minecraft.client.Options$1.binpatch
    Checksum: c26bb430 Exists: true
  Reading patch net.minecraft.client.Options$2.binpatch
    Checksum: e20308fc Exists: true
  Reading patch net.minecraft.client.Options$3.binpatch
    Checksum: 4aba055e Exists: true
  Reading patch net.minecraft.client.Options$4.binpatch
    Checksum: 12e3abb2 Exists: true
  Reading patch net.minecraft.client.Options$FieldAccess.binpatch
    Checksum: 72b5e01 Exists: true
  Reading patch net.minecraft.client.Options.binpatch
    Checksum: 3722a3cb Exists: true
  Reading patch net.minecraft.client.RecipeBookCategories$1.binpatch
    Checksum: 88c20a42 Exists: true
  Reading patch net.minecraft.client.RecipeBookCategories.binpatch
    Checksum: ab7bff81 Exists: true
  Reading patch net.minecraft.client.Screenshot.binpatch
    Checksum: b08c052f Exists: true
  Reading patch net.minecraft.client.ToggleKeyMapping.binpatch
    Checksum: 2f577cb0 Exists: true
  Reading patch net.minecraft.client.User$Type.binpatch
    Checksum: bbc092a8 Exists: true
  Reading patch net.minecraft.client.User.binpatch
    Checksum: 626f4d3b Exists: true
  Reading patch net.minecraft.client.color.block.BlockColors.binpatch
    Checksum: f51d4340 Exists: true
  Reading patch net.minecraft.client.color.item.ItemColors.binpatch
    Checksum: a629501b Exists: true
  Reading patch net.minecraft.client.gui.Gui$HeartType.binpatch
    Checksum: e45dccf2 Exists: true
  Reading patch net.minecraft.client.gui.Gui.binpatch
    Checksum: 58380b39 Exists: true
  Reading patch net.minecraft.client.gui.MapRenderer$MapInstance.binpatch
    Checksum: 1b517ffa Exists: true
  Reading patch net.minecraft.client.gui.MapRenderer.binpatch
    Checksum: 9ff84cce Exists: true
  Reading patch net.minecraft.client.gui.components.AbstractSelectionList$Entry.binpatch
    Checksum: 5f37ef3e Exists: true
  Reading patch net.minecraft.client.gui.components.AbstractSelectionList$SelectionDirection.binpatch
    Checksum: f269d6d4 Exists: true
  Reading patch net.minecraft.client.gui.components.AbstractSelectionList$TrackedList.binpatch
    Checksum: 85b2dbd2 Exists: true
  Reading patch net.minecraft.client.gui.components.AbstractSelectionList.binpatch
    Checksum: a3c5b475 Exists: true
  Reading patch net.minecraft.client.gui.components.AbstractWidget.binpatch
    Checksum: 6c99be4e Exists: true
  Reading patch net.minecraft.client.gui.components.BossHealthOverlay$1.binpatch
    Checksum: e7af00cb Exists: true
  Reading patch net.minecraft.client.gui.components.BossHealthOverlay.binpatch
    Checksum: 34309b52 Exists: true
  Reading patch net.minecraft.client.gui.components.DebugScreenOverlay$1.binpatch
    Checksum: 5a98fbdb Exists: true
  Reading patch net.minecraft.client.gui.components.DebugScreenOverlay$AllocationRateCalculator.binpatch
    Checksum: 750335fa Exists: true
  Reading patch net.minecraft.client.gui.components.DebugScreenOverlay.binpatch
    Checksum: a718d5a3 Exists: true
  Reading patch net.minecraft.client.gui.screens.DeathScreen.binpatch
    Checksum: e36b05f7 Exists: true
  Reading patch net.minecraft.client.gui.screens.LoadingOverlay$LogoTexture.binpatch
    Checksum: 4363779d Exists: true
  Reading patch net.minecraft.client.gui.screens.LoadingOverlay.binpatch
    Checksum: b4716976 Exists: true
  Reading patch net.minecraft.client.gui.screens.MenuScreens$ScreenConstructor.binpatch
    Checksum: 2d838959 Exists: true
  Reading patch net.minecraft.client.gui.screens.MenuScreens.binpatch
    Checksum: b7ea9744 Exists: true
  Reading patch net.minecraft.client.gui.screens.OptionsScreen.binpatch
    Checksum: 71834c9a Exists: true
  Reading patch net.minecraft.client.gui.screens.Screen$NarratableSearchResult.binpatch
    Checksum: 2f8373aa Exists: true
  Reading patch net.minecraft.client.gui.screens.Screen.binpatch
    Checksum: 3130e386 Exists: true
  Reading patch net.minecraft.client.gui.screens.TitleScreen$1.binpatch
    Checksum: 4a94210d Exists: true
  Reading patch net.minecraft.client.gui.screens.TitleScreen$WarningLabel.binpatch
    Checksum: 1b002ae6 Exists: true
  Reading patch net.minecraft.client.gui.screens.TitleScreen.binpatch
    Checksum: eaea13ea Exists: true
  Reading patch net.minecraft.client.gui.screens.advancements.AdvancementTab.binpatch
    Checksum: 804242f4 Exists: true
  Reading patch net.minecraft.client.gui.screens.advancements.AdvancementTabType$1.binpatch
    Checksum: 21011216 Exists: true
  Reading patch net.minecraft.client.gui.screens.advancements.AdvancementTabType.binpatch
    Checksum: e75138f9 Exists: true
  Reading patch net.minecraft.client.gui.screens.advancements.AdvancementsScreen.binpatch
    Checksum: 77d5aad4 Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsList$CategoryEntry$1.binpatch
    Checksum: 59ab82f2 Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsList$CategoryEntry.binpatch
    Checksum: 6ceea3c5 Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsList$Entry.binpatch
    Checksum: e3212136 Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsList$KeyEntry$1.binpatch
    Checksum: f27f269b Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsList$KeyEntry$2.binpatch
    Checksum: dda3c9fe Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsList$KeyEntry.binpatch
    Checksum: 6ab73aed Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsList.binpatch
    Checksum: 35f6695 Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsScreen.binpatch
    Checksum: ab0a0937 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.AbstractContainerScreen.binpatch
    Checksum: d095cfb2 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen$CustomCreativeSlot.binpatch
    Checksum: 6997a7d7 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen$ItemPickerMenu.binpatch
    Checksum: e607e581 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen$SlotWrapper.binpatch
    Checksum: d16ae6a2 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen.binpatch
    Checksum: cbf3154f Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.EffectRenderingInventoryScreen.binpatch
    Checksum: df687333 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.EnchantmentScreen.binpatch
    Checksum: e29cba22 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.MerchantScreen$TradeOfferButton.binpatch
    Checksum: 5083b411 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.MerchantScreen.binpatch
    Checksum: 9340a0e9 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.tooltip.ClientTooltipComponent.binpatch
    Checksum: 11df33c3 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.JoinMultiplayerScreen.binpatch
    Checksum: 334ba256 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList$Entry.binpatch
    Checksum: 5ed32a1c Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList$LANHeader.binpatch
    Checksum: e3140ed4 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList$NetworkServerEntry.binpatch
    Checksum: 5fb2f6e0 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList$OnlineServerEntry.binpatch
    Checksum: c29e60f5 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList.binpatch
    Checksum: 47f92d5f Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel$Entry.binpatch
    Checksum: 5565957b Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel$EntryBase.binpatch
    Checksum: ff319b5 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel$SelectedPackEntry.binpatch
    Checksum: 8d51a336 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel$UnselectedPackEntry.binpatch
    Checksum: 719ca7c0 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel.binpatch
    Checksum: e6aecc23 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionScreen$1.binpatch
    Checksum: 7b525d85 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionScreen$Watcher.binpatch
    Checksum: 5c458a9b Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionScreen.binpatch
    Checksum: 667a4705 Exists: true
  Reading patch net.minecraft.client.gui.screens.recipebook.RecipeBookComponent.binpatch
    Checksum: d433d854 Exists: true
  Reading patch net.minecraft.client.gui.screens.recipebook.RecipeBookPage.binpatch
    Checksum: 5ad8c36b Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$1.binpatch
    Checksum: 2a626392 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$SelectedGameMode.binpatch
    Checksum: 5b1cb9d5 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen.binpatch
    Checksum: d8ba4858 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldOpenFlows.binpatch
    Checksum: c29b38c6 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldSelectionList$Entry.binpatch
    Checksum: 8fe751d0 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldSelectionList$LoadingHeader.binpatch
    Checksum: 64ca58d0 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldSelectionList$WorldListEntry.binpatch
    Checksum: 8fdf294e Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldSelectionList.binpatch
    Checksum: cc7b738a Exists: true
  Reading patch net.minecraft.client.main.Main$1.binpatch
    Checksum: 4f63f67d Exists: true
  Reading patch net.minecraft.client.main.Main$2.binpatch
    Checksum: c4d930e7 Exists: true
  Reading patch net.minecraft.client.main.Main$3.binpatch
    Checksum: 1095566b Exists: true
  Reading patch net.minecraft.client.main.Main.binpatch
    Checksum: 278ab647 Exists: true
  Reading patch net.minecraft.client.model.geom.LayerDefinitions.binpatch
    Checksum: c7adeb32 Exists: true
  Reading patch net.minecraft.client.model.geom.ModelLayers.binpatch
    Checksum: b7fa349d Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientChunkCache$Storage.binpatch
    Checksum: 423a7792 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientChunkCache.binpatch
    Checksum: 7e4b5099 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientHandshakePacketListenerImpl.binpatch
    Checksum: 2a4566f8 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientLevel$1.binpatch
    Checksum: a6b30481 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientLevel$ClientLevelData.binpatch
    Checksum: b80e88e1 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientLevel$EntityCallbacks.binpatch
    Checksum: 72748151 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientLevel.binpatch
    Checksum: cc9421b4 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientPacketListener$1.binpatch
    Checksum: 4237816 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientPacketListener.binpatch
    Checksum: e8b5bc03 Exists: true
  Reading patch net.minecraft.client.multiplayer.MultiPlayerGameMode.binpatch
    Checksum: 400211ee Exists: true
  Reading patch net.minecraft.client.multiplayer.PlayerInfo.binpatch
    Checksum: 74898452 Exists: true
  Reading patch net.minecraft.client.multiplayer.ProfileKeyPairManager.binpatch
    Checksum: 5ee88349 Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerData$ChatPreview.binpatch
    Checksum: 214882d5 Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerData$ServerPackStatus.binpatch
    Checksum: 2f41e142 Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerData.binpatch
    Checksum: d4218d14 Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerStatusPinger$1.binpatch
    Checksum: 167604d8 Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerStatusPinger$2$1.binpatch
    Checksum: c9c8ad77 Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerStatusPinger$2.binpatch
    Checksum: c83ec2c5 Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerStatusPinger.binpatch
    Checksum: 7d21d06f Exists: true
  Reading patch net.minecraft.client.particle.BreakingItemParticle$Provider.binpatch
    Checksum: 56b125c5 Exists: true
  Reading patch net.minecraft.client.particle.BreakingItemParticle$SlimeProvider.binpatch
    Checksum: 20355b8f Exists: true
  Reading patch net.minecraft.client.particle.BreakingItemParticle$SnowballProvider.binpatch
    Checksum: 4f405f88 Exists: true
  Reading patch net.minecraft.client.particle.BreakingItemParticle.binpatch
    Checksum: 3ec92e54 Exists: true
  Reading patch net.minecraft.client.particle.Particle.binpatch
    Checksum: c134be6b Exists: true
  Reading patch net.minecraft.client.particle.ParticleEngine$MutableSpriteSet.binpatch
    Checksum: 29b418ab Exists: true
  Reading patch net.minecraft.client.particle.ParticleEngine$SpriteParticleRegistration.binpatch
    Checksum: a1c8120f Exists: true
  Reading patch net.minecraft.client.particle.ParticleEngine.binpatch
    Checksum: da7ef28e Exists: true
  Reading patch net.minecraft.client.particle.TerrainParticle$Provider.binpatch
    Checksum: c714c282 Exists: true
  Reading patch net.minecraft.client.particle.TerrainParticle.binpatch
    Checksum: 8b3b72c1 Exists: true
  Reading patch net.minecraft.client.player.AbstractClientPlayer.binpatch
    Checksum: 2d0af818 Exists: true
  Reading patch net.minecraft.client.player.LocalPlayer.binpatch
    Checksum: a81d5047 Exists: true
  Reading patch net.minecraft.client.player.RemotePlayer.binpatch
    Checksum: aff68cac Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects$EndEffects.binpatch
    Checksum: 5c558f5a Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects$NetherEffects.binpatch
    Checksum: 15555760 Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects$OverworldEffects.binpatch
    Checksum: bc7f9851 Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects$SkyType.binpatch
    Checksum: 81d3b4ee Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects.binpatch
    Checksum: a4a773a5 Exists: true
  Reading patch net.minecraft.client.renderer.EffectInstance.binpatch
    Checksum: c813ac71 Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$BlindnessFogFunction.binpatch
    Checksum: b99d5a93 Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$DarknessFogFunction.binpatch
    Checksum: 38880575 Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$FogData.binpatch
    Checksum: 1f2a2089 Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$FogMode.binpatch
    Checksum: e491847d Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$MobEffectFogFunction.binpatch
    Checksum: c791abd Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer.binpatch
    Checksum: cafe637d Exists: true
  Reading patch net.minecraft.client.renderer.GameRenderer.binpatch
    Checksum: 6f928780 Exists: true
  Reading patch net.minecraft.client.renderer.ItemBlockRenderTypes.binpatch
    Checksum: 1b9ffad3 Exists: true
  Reading patch net.minecraft.client.renderer.ItemInHandRenderer$1.binpatch
    Checksum: 26dc0f06 Exists: true
  Reading patch net.minecraft.client.renderer.ItemInHandRenderer$HandRenderSelection.binpatch
    Checksum: a0d78625 Exists: true
  Reading patch net.minecraft.client.renderer.ItemInHandRenderer.binpatch
    Checksum: 50648cb6 Exists: true
  Reading patch net.minecraft.client.renderer.ItemModelShaper.binpatch
    Checksum: ecf4baf1 Exists: true
  Reading patch net.minecraft.client.renderer.LevelRenderer$RenderChunkInfo.binpatch
    Checksum: 5bf7369 Exists: true
  Reading patch net.minecraft.client.renderer.LevelRenderer$RenderChunkStorage.binpatch
    Checksum: dcfc6960 Exists: true
  Reading patch net.minecraft.client.renderer.LevelRenderer$RenderInfoMap.binpatch
    Checksum: 8426156c Exists: true
  Reading patch net.minecraft.client.renderer.LevelRenderer$TransparencyShaderException.binpatch
    Checksum: 87a5e720 Exists: true
  Reading patch net.minecraft.client.renderer.LevelRenderer.binpatch
    Checksum: 2ec64201 Exists: true
  Reading patch net.minecraft.client.renderer.LightTexture.binpatch
    Checksum: 50a40109 Exists: true
  Reading patch net.minecraft.client.renderer.PostChain.binpatch
    Checksum: 2754d868 Exists: true
  Reading patch net.minecraft.client.renderer.RenderType$CompositeRenderType.binpatch
    Checksum: bbe8794a Exists: true
  Reading patch net.minecraft.client.renderer.RenderType$CompositeState$CompositeStateBuilder.binpatch
    Checksum: f6a6b12c Exists: true
  Reading patch net.minecraft.client.renderer.RenderType$CompositeState.binpatch
    Checksum: a6b1697a Exists: true
  Reading patch net.minecraft.client.renderer.RenderType$OutlineProperty.binpatch
    Checksum: 1c61ea23 Exists: true
  Reading patch net.minecraft.client.renderer.RenderType.binpatch
    Checksum: 3934565c Exists: true
  Reading patch net.minecraft.client.renderer.ScreenEffectRenderer.binpatch
    Checksum: d6e93622 Exists: true
  Reading patch net.minecraft.client.renderer.ShaderInstance$1.binpatch
    Checksum: 604808ce Exists: true
  Reading patch net.minecraft.client.renderer.ShaderInstance.binpatch
    Checksum: ed487630 Exists: true
  Reading patch net.minecraft.client.renderer.Sheets$1.binpatch
    Checksum: 6a40385 Exists: true
  Reading patch net.minecraft.client.renderer.Sheets.binpatch
    Checksum: 7bca3d7 Exists: true
  Reading patch net.minecraft.client.renderer.block.BlockModelShaper.binpatch
    Checksum: d1ddd99c Exists: true
  Reading patch net.minecraft.client.renderer.block.BlockRenderDispatcher$1.binpatch
    Checksum: b7d70301 Exists: true
  Reading patch net.minecraft.client.renderer.block.BlockRenderDispatcher.binpatch
    Checksum: fb776657 Exists: true
  Reading patch net.minecraft.client.renderer.block.LiquidBlockRenderer$1.binpatch
    Checksum: 50b6f38a Exists: true
  Reading patch net.minecraft.client.renderer.block.LiquidBlockRenderer.binpatch
    Checksum: 9260c187 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$1.binpatch
    Checksum: e7c10e3c Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$AdjacencyInfo.binpatch
    Checksum: 73c242aa Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$AmbientOcclusionFace.binpatch
    Checksum: 6c422fbd Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$AmbientVertexRemap.binpatch
    Checksum: ccd5fe92 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$Cache$1.binpatch
    Checksum: afe532ba Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$Cache$2.binpatch
    Checksum: a23e339e Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$Cache.binpatch
    Checksum: 63a7dd74 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$SizeInfo.binpatch
    Checksum: d4a0bf7 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer.binpatch
    Checksum: 1cadfa08 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockElementFace$Deserializer.binpatch
    Checksum: 5e04fe63 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockElementFace.binpatch
    Checksum: 150638c Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockModel$Deserializer.binpatch
    Checksum: 4218de9e Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockModel$GuiLight.binpatch
    Checksum: 48b3b8ef Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockModel$LoopException.binpatch
    Checksum: 4338cb23 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockModel.binpatch
    Checksum: c1353d7e Exists: true
  Reading patch net.minecraft.client.renderer.block.model.FaceBakery$1.binpatch
    Checksum: b106fed7 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.FaceBakery.binpatch
    Checksum: c8af7dcf Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemModelGenerator$1.binpatch
    Checksum: 3ea637a7 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemModelGenerator$Span.binpatch
    Checksum: a70190c2 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemModelGenerator$SpanFacing.binpatch
    Checksum: 26c1a67d Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemModelGenerator.binpatch
    Checksum: 2f73bc1d Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemOverrides$BakedOverride.binpatch
    Checksum: 6d48c585 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemOverrides$PropertyMatcher.binpatch
    Checksum: cf0dd233 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemOverrides.binpatch
    Checksum: 56c793f2 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransform$Deserializer.binpatch
    Checksum: a8db8cb9 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransform.binpatch
    Checksum: c5fe53ed Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransforms$1.binpatch
    Checksum: 1abc7690 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransforms$Deserializer.binpatch
    Checksum: 34a15a4f Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransforms$TransformType.binpatch
    Checksum: b0424b32 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransforms.binpatch
    Checksum: 172c69e9 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.MultiVariant$Deserializer.binpatch
    Checksum: c4664264 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.MultiVariant.binpatch
    Checksum: 43b3a8aa Exists: true
  Reading patch net.minecraft.client.renderer.blockentity.BlockEntityRenderers.binpatch
    Checksum: 3db978fd Exists: true
  Reading patch net.minecraft.client.renderer.blockentity.ChestRenderer.binpatch
    Checksum: 6f7c266d Exists: true
  Reading patch net.minecraft.client.renderer.blockentity.PistonHeadRenderer.binpatch
    Checksum: fe7407f0 Exists: true
  Reading patch net.minecraft.client.renderer.blockentity.SkullBlockRenderer.binpatch
    Checksum: 9fe05e21 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$ChunkTaskResult.binpatch
    Checksum: 423ed0ee Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$CompiledChunk$1.binpatch
    Checksum: 9d1d14ad Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$CompiledChunk.binpatch
    Checksum: 11c8fefd Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$RenderChunk$ChunkCompileTask.binpatch
    Checksum: 8550e747 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$RenderChunk$RebuildTask$CompileResults.binpatch
    Checksum: 1b7966b6 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$RenderChunk$RebuildTask.binpatch
    Checksum: 2d48b8ad Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$RenderChunk$ResortTransparencyTask.binpatch
    Checksum: 3618cea8 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$RenderChunk.binpatch
    Checksum: d26c79c9 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher.binpatch
    Checksum: a0e63548 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.RenderChunkRegion.binpatch
    Checksum: 65721d61 Exists: true
  Reading patch net.minecraft.client.renderer.entity.BoatRenderer.binpatch
    Checksum: b1fe57ad Exists: true
  Reading patch net.minecraft.client.renderer.entity.EntityRenderDispatcher.binpatch
    Checksum: 3b42cf9c Exists: true
  Reading patch net.minecraft.client.renderer.entity.EntityRenderer.binpatch
    Checksum: 99330b49 Exists: true
  Reading patch net.minecraft.client.renderer.entity.FallingBlockRenderer.binpatch
    Checksum: b8dbc832 Exists: true
  Reading patch net.minecraft.client.renderer.entity.FishingHookRenderer.binpatch
    Checksum: 63ed4ed9 Exists: true
  Reading patch net.minecraft.client.renderer.entity.ItemEntityRenderer.binpatch
    Checksum: 378a03d6 Exists: true
  Reading patch net.minecraft.client.renderer.entity.ItemFrameRenderer.binpatch
    Checksum: e5c68cda Exists: true
  Reading patch net.minecraft.client.renderer.entity.ItemRenderer.binpatch
    Checksum: 9b86e135 Exists: true
  Reading patch net.minecraft.client.renderer.entity.LivingEntityRenderer$1.binpatch
    Checksum: 9ec3921b Exists: true
  Reading patch net.minecraft.client.renderer.entity.LivingEntityRenderer.binpatch
    Checksum: 9816decf Exists: true
  Reading patch net.minecraft.client.renderer.entity.layers.ElytraLayer.binpatch
    Checksum: eb0e5d25 Exists: true
  Reading patch net.minecraft.client.renderer.entity.layers.HumanoidArmorLayer$1.binpatch
    Checksum: 9e3c116d Exists: true
  Reading patch net.minecraft.client.renderer.entity.layers.HumanoidArmorLayer.binpatch
    Checksum: 87f4e050 Exists: true
  Reading patch net.minecraft.client.renderer.entity.player.PlayerRenderer.binpatch
    Checksum: eaef84c6 Exists: true
  Reading patch net.minecraft.client.renderer.item.ItemProperties$1.binpatch
    Checksum: 2faedd83 Exists: true
  Reading patch net.minecraft.client.renderer.item.ItemProperties.binpatch
    Checksum: abde852b Exists: true
  Reading patch net.minecraft.client.renderer.texture.AbstractTexture.binpatch
    Checksum: e878e7af Exists: true
  Reading patch net.minecraft.client.renderer.texture.MipmapGenerator.binpatch
    Checksum: 19159a20 Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher$Holder.binpatch
    Checksum: aeb0df3f Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher$Region.binpatch
    Checksum: 15ba72e4 Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher$SpriteLoader.binpatch
    Checksum: 7f2ed3a1 Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher.binpatch
    Checksum: f92f9d1b Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlas$Preparations.binpatch
    Checksum: 1a3398df Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlas.binpatch
    Checksum: efd3e885 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlasSprite$AnimatedTexture.binpatch
    Checksum: 5f87b621 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlasSprite$FrameInfo.binpatch
    Checksum: 642cce44 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlasSprite$Info.binpatch
    Checksum: 9de6752 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlasSprite$InterpolationData.binpatch
    Checksum: 9bd31891 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlasSprite.binpatch
    Checksum: 9cdbfd76 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureManager.binpatch
    Checksum: 6483208f Exists: true
  Reading patch net.minecraft.client.resources.language.ClientLanguage.binpatch
    Checksum: 817f52a5 Exists: true
  Reading patch net.minecraft.client.resources.language.I18n.binpatch
    Checksum: b338d773 Exists: true
  Reading patch net.minecraft.client.resources.language.LanguageInfo.binpatch
    Checksum: 2886ef95 Exists: true
  Reading patch net.minecraft.client.resources.model.BakedModel.binpatch
    Checksum: 5f06651a Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBakery$BlockStateDefinitionException.binpatch
    Checksum: 6e54d9d7 Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBakery$ModelGroupKey.binpatch
    Checksum: 12851a24 Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBakery.binpatch
    Checksum: 563c255f Exists: true
  Reading patch net.minecraft.client.resources.model.ModelManager.binpatch
    Checksum: 8b4b7f7c Exists: true
  Reading patch net.minecraft.client.resources.model.ModelResourceLocation.binpatch
    Checksum: e65c1131 Exists: true
  Reading patch net.minecraft.client.resources.model.MultiPartBakedModel$Builder.binpatch
    Checksum: ab1185ab Exists: true
  Reading patch net.minecraft.client.resources.model.MultiPartBakedModel.binpatch
    Checksum: e3e3d7d8 Exists: true
  Reading patch net.minecraft.client.resources.model.SimpleBakedModel$Builder.binpatch
    Checksum: 5f98b5a5 Exists: true
  Reading patch net.minecraft.client.resources.model.SimpleBakedModel.binpatch
    Checksum: b93e9774 Exists: true
  Reading patch net.minecraft.client.resources.model.WeightedBakedModel$Builder.binpatch
    Checksum: 1e985d9c Exists: true
  Reading patch net.minecraft.client.resources.model.WeightedBakedModel.binpatch
    Checksum: b7e8b23f Exists: true
  Reading patch net.minecraft.client.resources.sounds.SoundInstance$Attenuation.binpatch
    Checksum: b766acf3 Exists: true
  Reading patch net.minecraft.client.resources.sounds.SoundInstance.binpatch
    Checksum: 2d778a7f Exists: true
  Reading patch net.minecraft.client.server.IntegratedServer.binpatch
    Checksum: c94d1c79 Exists: true
  Reading patch net.minecraft.client.server.LanServerDetection$LanServerDetector.binpatch
    Checksum: a1073a81 Exists: true
  Reading patch net.minecraft.client.server.LanServerDetection$LanServerList.binpatch
    Checksum: b95014a8 Exists: true
  Reading patch net.minecraft.client.server.LanServerDetection.binpatch
    Checksum: 6b712221 Exists: true
  Reading patch net.minecraft.client.server.LanServerPinger.binpatch
    Checksum: 55ef6d15 Exists: true
  Reading patch net.minecraft.client.sounds.SoundEngine$DeviceCheckState.binpatch
    Checksum: 613da625 Exists: true
  Reading patch net.minecraft.client.sounds.SoundEngine.binpatch
    Checksum: cee40f99 Exists: true
  Reading patch net.minecraft.commands.CommandSourceStack.binpatch
    Checksum: 1ba4c549 Exists: true
  Reading patch net.minecraft.commands.Commands$CommandSelection.binpatch
    Checksum: df4e7fe0 Exists: true
  Reading patch net.minecraft.commands.Commands$ParseFunction.binpatch
    Checksum: 5c1f848f Exists: true
  Reading patch net.minecraft.commands.Commands.binpatch
    Checksum: ec5d4815 Exists: true
  Reading patch net.minecraft.commands.arguments.ObjectiveArgument.binpatch
    Checksum: 196b9bfa Exists: true
  Reading patch net.minecraft.commands.arguments.ResourceLocationArgument.binpatch
    Checksum: 6326efa0 Exists: true
  Reading patch net.minecraft.commands.arguments.TeamArgument.binpatch
    Checksum: b1d8431 Exists: true
  Reading patch net.minecraft.commands.arguments.coordinates.BlockPosArgument.binpatch
    Checksum: 2a9d2715 Exists: true
  Reading patch net.minecraft.commands.arguments.selector.EntitySelectorParser.binpatch
    Checksum: a0f018fa Exists: true
  Reading patch net.minecraft.commands.synchronization.ArgumentTypeInfos.binpatch
    Checksum: e9c32cd6 Exists: true
  Reading patch net.minecraft.core.Holder$Direct.binpatch
    Checksum: daa11593 Exists: true
  Reading patch net.minecraft.core.Holder$Kind.binpatch
    Checksum: 55c817ba Exists: true
  Reading patch net.minecraft.core.Holder$Reference$Type.binpatch
    Checksum: 73eb4a5a Exists: true
  Reading patch net.minecraft.core.Holder$Reference.binpatch
    Checksum: a10a354 Exists: true
  Reading patch net.minecraft.core.Holder.binpatch
    Checksum: a89be140 Exists: true
  Reading patch net.minecraft.core.MappedRegistry.binpatch
    Checksum: 6990225d Exists: true
  Reading patch net.minecraft.core.Registry$1.binpatch
    Checksum: ca8b268e Exists: true
  Reading patch net.minecraft.core.Registry$RegistryBootstrap.binpatch
    Checksum: ede39b30 Exists: true
  Reading patch net.minecraft.core.Registry.binpatch
    Checksum: 54f24805 Exists: true
  Reading patch net.minecraft.core.RegistryAccess$1.binpatch
    Checksum: cd9128eb Exists: true
  Reading patch net.minecraft.core.RegistryAccess$Frozen.binpatch
    Checksum: 24ee7a4f Exists: true
  Reading patch net.minecraft.core.RegistryAccess$ImmutableRegistryAccess.binpatch
    Checksum: cdc83f5a Exists: true
  Reading patch net.minecraft.core.RegistryAccess$RegistryData.binpatch
    Checksum: db705df8 Exists: true
  Reading patch net.minecraft.core.RegistryAccess$RegistryEntry.binpatch
    Checksum: 12ddf7d4 Exists: true
  Reading patch net.minecraft.core.RegistryAccess$Writable.binpatch
    Checksum: d18550a3 Exists: true
  Reading patch net.minecraft.core.RegistryAccess$WritableRegistryAccess.binpatch
    Checksum: e78e87d0 Exists: true
  Reading patch net.minecraft.core.RegistryAccess.binpatch
    Checksum: 46a1b048 Exists: true
  Reading patch net.minecraft.core.RegistryCodecs$1.binpatch
    Checksum: c4e4acf4 Exists: true
  Reading patch net.minecraft.core.RegistryCodecs$RegistryEntry.binpatch
    Checksum: 6c0f7823 Exists: true
  Reading patch net.minecraft.core.RegistryCodecs.binpatch
    Checksum: c3410964 Exists: true
  Reading patch net.minecraft.core.dispenser.BoatDispenseItemBehavior.binpatch
    Checksum: 943fd05c Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$1.binpatch
    Checksum: cbc3c53a Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$10.binpatch
    Checksum: 25ee592a Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$11.binpatch
    Checksum: bc9a7d70 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$12.binpatch
    Checksum: a7f1b52e Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$13.binpatch
    Checksum: 34b5a312 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$14.binpatch
    Checksum: 15dd18ae Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$15.binpatch
    Checksum: c02cfd1a Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$16.binpatch
    Checksum: 70fc7067 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$17.binpatch
    Checksum: 7411c201 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$18.binpatch
    Checksum: 3745aed8 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$19.binpatch
    Checksum: 8dc50ade Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$2.binpatch
    Checksum: 5ca5de15 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$20.binpatch
    Checksum: 7430ed6a Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$21.binpatch
    Checksum: 858a21f1 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$22.binpatch
    Checksum: 302fa306 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$23.binpatch
    Checksum: 7af4d997 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$24.binpatch
    Checksum: f2c32d88 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$25.binpatch
    Checksum: 3fa3800c Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$26.binpatch
    Checksum: f6ae7776 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$27.binpatch
    Checksum: 6ac5b0c2 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$3.binpatch
    Checksum: d97dcc62 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$4.binpatch
    Checksum: e0c8cf21 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$5.binpatch
    Checksum: f8accdda Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$6.binpatch
    Checksum: 6115fea7 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$7$1.binpatch
    Checksum: a70f77f7 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$7.binpatch
    Checksum: 7fea0fb4 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$8$1.binpatch
    Checksum: 5f7856 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$8.binpatch
    Checksum: ba771010 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$9.binpatch
    Checksum: f3d173b5 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior.binpatch
    Checksum: a46d5a6f Exists: true
  Reading patch net.minecraft.core.particles.BlockParticleOption$1.binpatch
    Checksum: 4a49a4d2 Exists: true
  Reading patch net.minecraft.core.particles.BlockParticleOption.binpatch
    Checksum: a4a783a6 Exists: true
  Reading patch net.minecraft.core.particles.ItemParticleOption$1.binpatch
    Checksum: 49ad0d33 Exists: true
  Reading patch net.minecraft.core.particles.ItemParticleOption.binpatch
    Checksum: bc956033 Exists: true
  Reading patch net.minecraft.data.BuiltinRegistries$RegistryBootstrap.binpatch
    Checksum: 35abb15e Exists: true
  Reading patch net.minecraft.data.BuiltinRegistries.binpatch
    Checksum: 70c88899 Exists: true
  Reading patch net.minecraft.data.DataGenerator$PathProvider.binpatch
    Checksum: c51984d0 Exists: true
  Reading patch net.minecraft.data.DataGenerator$Target.binpatch
    Checksum: ea64725d Exists: true
  Reading patch net.minecraft.data.DataGenerator.binpatch
    Checksum: 52099593 Exists: true
  Reading patch net.minecraft.data.HashCache$CacheUpdater.binpatch
    Checksum: 9d966fe0 Exists: true
  Reading patch net.minecraft.data.HashCache$ProviderCache.binpatch
    Checksum: 57e207ce Exists: true
  Reading patch net.minecraft.data.HashCache.binpatch
    Checksum: 38c32637 Exists: true
  Reading patch net.minecraft.data.Main.binpatch
    Checksum: b18441a9 Exists: true
  Reading patch net.minecraft.data.advancements.AdvancementProvider.binpatch
    Checksum: 4362276f Exists: true
  Reading patch net.minecraft.data.info.WorldgenRegistryDumpReport.binpatch
    Checksum: b9e99cc9 Exists: true
  Reading patch net.minecraft.data.loot.BlockLoot.binpatch
    Checksum: fbe868ad Exists: true
  Reading patch net.minecraft.data.loot.EntityLoot.binpatch
    Checksum: 21507af6 Exists: true
  Reading patch net.minecraft.data.loot.LootTableProvider.binpatch
    Checksum: 9aa36fc4 Exists: true
  Reading patch net.minecraft.data.recipes.RecipeProvider.binpatch
    Checksum: 84658f40 Exists: true
  Reading patch net.minecraft.data.tags.BannerPatternTagsProvider.binpatch
    Checksum: cd7e73af Exists: true
  Reading patch net.minecraft.data.tags.BiomeTagsProvider.binpatch
    Checksum: 49e8cacb Exists: true
  Reading patch net.minecraft.data.tags.BlockTagsProvider.binpatch
    Checksum: ec408abb Exists: true
  Reading patch net.minecraft.data.tags.CatVariantTagsProvider.binpatch
    Checksum: 77ee3652 Exists: true
  Reading patch net.minecraft.data.tags.EntityTypeTagsProvider.binpatch
    Checksum: 5c21c4a0 Exists: true
  Reading patch net.minecraft.data.tags.FlatLevelGeneratorPresetTagsProvider.binpatch
    Checksum: ab523976 Exists: true
  Reading patch net.minecraft.data.tags.FluidTagsProvider.binpatch
    Checksum: a4dad36d Exists: true
  Reading patch net.minecraft.data.tags.GameEventTagsProvider.binpatch
    Checksum: 9260c791 Exists: true
  Reading patch net.minecraft.data.tags.InstrumentTagsProvider.binpatch
    Checksum: faf21e7c Exists: true
  Reading patch net.minecraft.data.tags.ItemTagsProvider.binpatch
    Checksum: 5e19d94d Exists: true
  Reading patch net.minecraft.data.tags.PaintingVariantTagsProvider.binpatch
    Checksum: 9516c2c2 Exists: true
  Reading patch net.minecraft.data.tags.PoiTypeTagsProvider.binpatch
    Checksum: 4ba36c40 Exists: true
  Reading patch net.minecraft.data.tags.StructureTagsProvider.binpatch
    Checksum: d87d630c Exists: true
  Reading patch net.minecraft.data.tags.TagsProvider$TagAppender.binpatch
    Checksum: 607cfe2e Exists: true
  Reading patch net.minecraft.data.tags.TagsProvider.binpatch
    Checksum: 7e9930bd Exists: true
  Reading patch net.minecraft.data.tags.WorldPresetTagsProvider.binpatch
    Checksum: 388d0a88 Exists: true
  Reading patch net.minecraft.data.worldgen.biome.OverworldBiomes.binpatch
    Checksum: 53002ef6 Exists: true
  Reading patch net.minecraft.gametest.framework.GameTest.binpatch
    Checksum: d84da8fe Exists: true
  Reading patch net.minecraft.gametest.framework.GameTestRegistry.binpatch
    Checksum: b43b52c5 Exists: true
  Reading patch net.minecraft.gametest.framework.GameTestServer$1.binpatch
    Checksum: 9f37b48f Exists: true
  Reading patch net.minecraft.gametest.framework.GameTestServer.binpatch
    Checksum: 2974e687 Exists: true
  Reading patch net.minecraft.gametest.framework.StructureUtils$1.binpatch
    Checksum: 9d05db43 Exists: true
  Reading patch net.minecraft.gametest.framework.StructureUtils.binpatch
    Checksum: e6f4d949 Exists: true
  Reading patch net.minecraft.locale.Language$1.binpatch
    Checksum: f7b4e1b9 Exists: true
  Reading patch net.minecraft.locale.Language.binpatch
    Checksum: af330b13 Exists: true
  Reading patch net.minecraft.nbt.CompoundTag$1.binpatch
    Checksum: 9cca62df Exists: true
  Reading patch net.minecraft.nbt.CompoundTag$2.binpatch
    Checksum: f18738e8 Exists: true
  Reading patch net.minecraft.nbt.CompoundTag.binpatch
    Checksum: 709fe7c6 Exists: true
  Reading patch net.minecraft.nbt.NbtAccounter$1.binpatch
    Checksum: 7fc2665a Exists: true
  Reading patch net.minecraft.nbt.NbtAccounter.binpatch
    Checksum: f754458a Exists: true
  Reading patch net.minecraft.nbt.NbtIo$1.binpatch
    Checksum: e015ceb1 Exists: true
  Reading patch net.minecraft.nbt.NbtIo.binpatch
    Checksum: 65c02748 Exists: true
  Reading patch net.minecraft.nbt.StringTag$1.binpatch
    Checksum: 94a76b88 Exists: true
  Reading patch net.minecraft.nbt.StringTag.binpatch
    Checksum: 51084b57 Exists: true
  Reading patch net.minecraft.network.CompressionEncoder.binpatch
    Checksum: 555e1910 Exists: true
  Reading patch net.minecraft.network.Connection$1.binpatch
    Checksum: d843a4fc Exists: true
  Reading patch net.minecraft.network.Connection$2.binpatch
    Checksum: 635c4854 Exists: true
  Reading patch net.minecraft.network.Connection$PacketHolder.binpatch
    Checksum: 3eabaa6b Exists: true
  Reading patch net.minecraft.network.Connection.binpatch
    Checksum: ff6a59e Exists: true
  Reading patch net.minecraft.network.FriendlyByteBuf$Reader.binpatch
    Checksum: 6a1a6150 Exists: true
  Reading patch net.minecraft.network.FriendlyByteBuf$Writer.binpatch
    Checksum: 190772c8 Exists: true
  Reading patch net.minecraft.network.FriendlyByteBuf.binpatch
    Checksum: 73597c24 Exists: true
  Reading patch net.minecraft.network.chat.ChatDecorator.binpatch
    Checksum: 53b1fdc1 Exists: true
  Reading patch net.minecraft.network.chat.PlayerChatMessage.binpatch
    Checksum: 78e18529 Exists: true
  Reading patch net.minecraft.network.chat.contents.TranslatableContents.binpatch
    Checksum: 5e66b862 Exists: true
  Reading patch net.minecraft.network.protocol.game.ClientboundCustomPayloadPacket.binpatch
    Checksum: 7ca9ac42 Exists: true
  Reading patch net.minecraft.network.protocol.game.ServerboundContainerClickPacket.binpatch
    Checksum: 184b0a5d Exists: true
  Reading patch net.minecraft.network.protocol.game.ServerboundCustomPayloadPacket.binpatch
    Checksum: 421efdef Exists: true
  Reading patch net.minecraft.network.protocol.game.ServerboundSetCreativeModeSlotPacket.binpatch
    Checksum: 87534bfe Exists: true
  Reading patch net.minecraft.network.protocol.handshake.ClientIntentionPacket.binpatch
    Checksum: ed733107 Exists: true
  Reading patch net.minecraft.network.protocol.login.ClientboundCustomQueryPacket.binpatch
    Checksum: 3c3915c7 Exists: true
  Reading patch net.minecraft.network.protocol.login.ServerboundCustomQueryPacket.binpatch
    Checksum: b65a9f72 Exists: true
  Reading patch net.minecraft.network.protocol.status.ClientboundStatusResponsePacket.binpatch
    Checksum: e5086754 Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus$Players$Serializer.binpatch
    Checksum: ecd02634 Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus$Players.binpatch
    Checksum: d53127b Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus$Serializer.binpatch
    Checksum: 16b4d1db Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus$Version$Serializer.binpatch
    Checksum: d808e2a8 Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus$Version.binpatch
    Checksum: 3a54d89b Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus.binpatch
    Checksum: ebe79a3 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$1.binpatch
    Checksum: e6be2f42 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$2.binpatch
    Checksum: dde94162 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$3.binpatch
    Checksum: 59accc92 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$4.binpatch
    Checksum: ab2702c4 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$5.binpatch
    Checksum: d74d1dba Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$6.binpatch
    Checksum: 1a6e2129 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$7.binpatch
    Checksum: 69811da3 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers.binpatch
    Checksum: f0e5d1 Exists: true
  Reading patch net.minecraft.network.syncher.SynchedEntityData$DataItem.binpatch
    Checksum: f44d5167 Exists: true
  Reading patch net.minecraft.network.syncher.SynchedEntityData.binpatch
    Checksum: 90a08702 Exists: true
  Reading patch net.minecraft.recipebook.PlaceRecipe.binpatch
    Checksum: fbdbe404 Exists: true
  Reading patch net.minecraft.resources.RegistryResourceAccess$1.binpatch
    Checksum: 7c7e2b28 Exists: true
  Reading patch net.minecraft.resources.RegistryResourceAccess$EntryThunk.binpatch
    Checksum: 49a15766 Exists: true
  Reading patch net.minecraft.resources.RegistryResourceAccess$InMemoryStorage$Entry.binpatch
    Checksum: 3b9c4d73 Exists: true
  Reading patch net.minecraft.resources.RegistryResourceAccess$InMemoryStorage.binpatch
    Checksum: c223b266 Exists: true
  Reading patch net.minecraft.resources.RegistryResourceAccess$ParsedEntry.binpatch
    Checksum: d31e1eaf Exists: true
  Reading patch net.minecraft.resources.RegistryResourceAccess.binpatch
    Checksum: 4d0643eb Exists: true
  Reading patch net.minecraft.resources.ResourceKey.binpatch
    Checksum: 5c35627c Exists: true
  Reading patch net.minecraft.resources.ResourceLocation$Serializer.binpatch
    Checksum: b710f032 Exists: true
  Reading patch net.minecraft.resources.ResourceLocation.binpatch
    Checksum: 2cc43893 Exists: true
  Reading patch net.minecraft.server.Bootstrap$1.binpatch
    Checksum: beda32d0 Exists: true
  Reading patch net.minecraft.server.Bootstrap.binpatch
    Checksum: 89c74f5e Exists: true
  Reading patch net.minecraft.server.Eula.binpatch
    Checksum: 282c9e05 Exists: true
  Reading patch net.minecraft.server.Main$1.binpatch
    Checksum: 6aefcc27 Exists: true
  Reading patch net.minecraft.server.Main.binpatch
    Checksum: fdaf750d Exists: true
  Reading patch net.minecraft.server.MinecraftServer$1.binpatch
    Checksum: 862e9fa9 Exists: true
  Reading patch net.minecraft.server.MinecraftServer$ReloadableResources.binpatch
    Checksum: ffd0bd4d Exists: true
  Reading patch net.minecraft.server.MinecraftServer$ServerResourcePackInfo.binpatch
    Checksum: 47049ffa Exists: true
  Reading patch net.minecraft.server.MinecraftServer$TimeProfiler$1.binpatch
    Checksum: 1f2bf4ca Exists: true
  Reading patch net.minecraft.server.MinecraftServer$TimeProfiler.binpatch
    Checksum: cd451218 Exists: true
  Reading patch net.minecraft.server.MinecraftServer.binpatch
    Checksum: 77785ecd Exists: true
  Reading patch net.minecraft.server.PlayerAdvancements$1.binpatch
    Checksum: 888fc9f3 Exists: true
  Reading patch net.minecraft.server.PlayerAdvancements.binpatch
    Checksum: d0d9878 Exists: true
  Reading patch net.minecraft.server.ReloadableServerResources.binpatch
    Checksum: e9b4bf44 Exists: true
  Reading patch net.minecraft.server.ServerAdvancementManager.binpatch
    Checksum: 9e4b8685 Exists: true
  Reading patch net.minecraft.server.commands.SpreadPlayersCommand$Position.binpatch
    Checksum: 9a40bcb9 Exists: true
  Reading patch net.minecraft.server.commands.SpreadPlayersCommand.binpatch
    Checksum: 1bc29b12 Exists: true
  Reading patch net.minecraft.server.commands.TeleportCommand$LookAt.binpatch
    Checksum: ddf19bc3 Exists: true
  Reading patch net.minecraft.server.commands.TeleportCommand.binpatch
    Checksum: 3121639c Exists: true
  Reading patch net.minecraft.server.dedicated.DedicatedServer$1.binpatch
    Checksum: 3d3ef5d1 Exists: true
  Reading patch net.minecraft.server.dedicated.DedicatedServer.binpatch
    Checksum: ec7f6539 Exists: true
  Reading patch net.minecraft.server.dedicated.ServerWatchdog$1.binpatch
    Checksum: 9534c9c9 Exists: true
  Reading patch net.minecraft.server.dedicated.ServerWatchdog.binpatch
    Checksum: d03cf47d Exists: true
  Reading patch net.minecraft.server.dedicated.Settings$MutableValue.binpatch
    Checksum: d8df92ee Exists: true
  Reading patch net.minecraft.server.dedicated.Settings.binpatch
    Checksum: de4856b Exists: true
  Reading patch net.minecraft.server.gui.MinecraftServerGui$1.binpatch
    Checksum: 8c9dbf95 Exists: true
  Reading patch net.minecraft.server.gui.MinecraftServerGui$2.binpatch
    Checksum: 4f24d731 Exists: true
  Reading patch net.minecraft.server.gui.MinecraftServerGui.binpatch
    Checksum: 304a21d1 Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder$1.binpatch
    Checksum: e7aeab63 Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder$ChunkLoadingFailure$1.binpatch
    Checksum: cca1ac55 Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder$ChunkLoadingFailure.binpatch
    Checksum: 2feb9659 Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder$ChunkSaveDebug.binpatch
    Checksum: e0b70970 Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder$FullChunkStatus.binpatch
    Checksum: 6efdb001 Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder$LevelChangeListener.binpatch
    Checksum: ecd1ae67 Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder$PlayerProvider.binpatch
    Checksum: 3f729cf7 Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder.binpatch
    Checksum: d8571b61 Exists: true
  Reading patch net.minecraft.server.level.ChunkMap$1.binpatch
    Checksum: 966ce405 Exists: true
  Reading patch net.minecraft.server.level.ChunkMap$2.binpatch
    Checksum: 76ab6af3 Exists: true
  Reading patch net.minecraft.server.level.ChunkMap$DistanceManager.binpatch
    Checksum: 97fcc924 Exists: true
  Reading patch net.minecraft.server.level.ChunkMap$TrackedEntity.binpatch
    Checksum: 4e77ef25 Exists: true
  Reading patch net.minecraft.server.level.ChunkMap.binpatch
    Checksum: a1139a35 Exists: true
  Reading patch net.minecraft.server.level.DistanceManager$ChunkTicketTracker.binpatch
    Checksum: 310c8d90 Exists: true
  Reading patch net.minecraft.server.level.DistanceManager$FixedPlayerDistanceChunkTracker.binpatch
    Checksum: 6fa0e9a7 Exists: true
  Reading patch net.minecraft.server.level.DistanceManager$PlayerTicketTracker.binpatch
    Checksum: 48b636a6 Exists: true
  Reading patch net.minecraft.server.level.DistanceManager.binpatch
    Checksum: 43842930 Exists: true
  Reading patch net.minecraft.server.level.ServerChunkCache$ChunkAndHolder.binpatch
    Checksum: 7a5f26a9 Exists: true
  Reading patch net.minecraft.server.level.ServerChunkCache$MainThreadExecutor.binpatch
    Checksum: 183832b4 Exists: true
  Reading patch net.minecraft.server.level.ServerChunkCache.binpatch
    Checksum: 6e52c495 Exists: true
  Reading patch net.minecraft.server.level.ServerEntity.binpatch
    Checksum: 83d17702 Exists: true
  Reading patch net.minecraft.server.level.ServerLevel$EntityCallbacks.binpatch
    Checksum: eb0be052 Exists: true
  Reading patch net.minecraft.server.level.ServerLevel.binpatch
    Checksum: c3b08117 Exists: true
  Reading patch net.minecraft.server.level.ServerPlayer$1.binpatch
    Checksum: 99e59a5f Exists: true
  Reading patch net.minecraft.server.level.ServerPlayer$2.binpatch
    Checksum: 10957c11 Exists: true
  Reading patch net.minecraft.server.level.ServerPlayer$3.binpatch
    Checksum: 1df3cbb9 Exists: true
  Reading patch net.minecraft.server.level.ServerPlayer.binpatch
    Checksum: 77130826 Exists: true
  Reading patch net.minecraft.server.level.ServerPlayerGameMode.binpatch
    Checksum: b7f48ded Exists: true
  Reading patch net.minecraft.server.level.Ticket.binpatch
    Checksum: 96309248 Exists: true
  Reading patch net.minecraft.server.network.MemoryServerHandshakePacketListenerImpl.binpatch
    Checksum: 7cacf9e6 Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener$1.binpatch
    Checksum: 3dcfd1a Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener$2.binpatch
    Checksum: 6259687b Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener$LatencySimulator$DelayedMessage.binpatch
    Checksum: 4e0405a9 Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener$LatencySimulator.binpatch
    Checksum: aa9e8c63 Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener.binpatch
    Checksum: 2729eb85 Exists: true
  Reading patch net.minecraft.server.network.ServerGamePacketListenerImpl$1.binpatch
    Checksum: 404a5847 Exists: true
  Reading patch net.minecraft.server.network.ServerGamePacketListenerImpl$2.binpatch
    Checksum: 317af06a Exists: true
  Reading patch net.minecraft.server.network.ServerGamePacketListenerImpl$EntityInteraction.binpatch
    Checksum: d8fac060 Exists: true
  Reading patch net.minecraft.server.network.ServerGamePacketListenerImpl.binpatch
    Checksum: 4b4a43f4 Exists: true
  Reading patch net.minecraft.server.network.ServerHandshakePacketListenerImpl$1.binpatch
    Checksum: 430bd501 Exists: true
  Reading patch net.minecraft.server.network.ServerHandshakePacketListenerImpl.binpatch
    Checksum: 81131501 Exists: true
  Reading patch net.minecraft.server.network.ServerLoginPacketListenerImpl$1.binpatch
    Checksum: 11c2c145 Exists: true
  Reading patch net.minecraft.server.network.ServerLoginPacketListenerImpl$PublicKeyParseException.binpatch
    Checksum: 4a0308e6 Exists: true
  Reading patch net.minecraft.server.network.ServerLoginPacketListenerImpl$State.binpatch
    Checksum: 1907cd04 Exists: true
  Reading patch net.minecraft.server.network.ServerLoginPacketListenerImpl.binpatch
    Checksum: f4d341bc Exists: true
  Reading patch net.minecraft.server.packs.AbstractPackResources.binpatch
    Checksum: 72d60cbf Exists: true
  Reading patch net.minecraft.server.packs.PackResources.binpatch
    Checksum: b04e5add Exists: true
  Reading patch net.minecraft.server.packs.VanillaPackResources.binpatch
    Checksum: 7cb80491 Exists: true
  Reading patch net.minecraft.server.packs.metadata.pack.PackMetadataSection.binpatch
    Checksum: efdf6e7 Exists: true
  Reading patch net.minecraft.server.packs.metadata.pack.PackMetadataSectionSerializer.binpatch
    Checksum: 1eff6695 Exists: true
  Reading patch net.minecraft.server.packs.repository.Pack$PackConstructor.binpatch
    Checksum: 482dbdf2 Exists: true
  Reading patch net.minecraft.server.packs.repository.Pack$Position.binpatch
    Checksum: 5fa0168b Exists: true
  Reading patch net.minecraft.server.packs.repository.Pack.binpatch
    Checksum: acdd3b0 Exists: true
  Reading patch net.minecraft.server.packs.repository.PackCompatibility.binpatch
    Checksum: 283d4c6d Exists: true
  Reading patch net.minecraft.server.packs.repository.PackRepository.binpatch
    Checksum: 35cbbbb5 Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$EntryStack.binpatch
    Checksum: b2aa8168 Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$LeakedResourceWarningInputStream.binpatch
    Checksum: 358d9496 Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$PackEntry.binpatch
    Checksum: e0f1b7c Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$SinglePackResourceThunkSupplier.binpatch
    Checksum: c20506d8 Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager.binpatch
    Checksum: ff553aa0 Exists: true
  Reading patch net.minecraft.server.packs.resources.MultiPackResourceManager.binpatch
    Checksum: 795b1bdc Exists: true
  Reading patch net.minecraft.server.packs.resources.ReloadableResourceManager.binpatch
    Checksum: c92f07bf Exists: true
  Reading patch net.minecraft.server.packs.resources.SimpleJsonResourceReloadListener.binpatch
    Checksum: 5ecc35f6 Exists: true
  Reading patch net.minecraft.server.players.PlayerList$1.binpatch
    Checksum: fbb2729e Exists: true
  Reading patch net.minecraft.server.players.PlayerList.binpatch
    Checksum: c95dae63 Exists: true
  Reading patch net.minecraft.server.rcon.RconConsoleSource.binpatch
    Checksum: d831f09b Exists: true
  Reading patch net.minecraft.server.rcon.thread.RconClient.binpatch
    Checksum: cc9790af Exists: true
  Reading patch net.minecraft.stats.RecipeBookSettings$TypeSettings.binpatch
    Checksum: 220e8249 Exists: true
  Reading patch net.minecraft.stats.RecipeBookSettings.binpatch
    Checksum: 6c5fb3c2 Exists: true
  Reading patch net.minecraft.tags.BlockTags.binpatch
    Checksum: 2b71abd9 Exists: true
  Reading patch net.minecraft.tags.FluidTags.binpatch
    Checksum: 46305e15 Exists: true
  Reading patch net.minecraft.tags.ItemTags.binpatch
    Checksum: cc5a14e Exists: true
  Reading patch net.minecraft.tags.TagBuilder.binpatch
    Checksum: 42b7a85e Exists: true
  Reading patch net.minecraft.tags.TagEntry$Lookup.binpatch
    Checksum: e4f7e602 Exists: true
  Reading patch net.minecraft.tags.TagEntry.binpatch
    Checksum: 4891f402 Exists: true
  Reading patch net.minecraft.tags.TagLoader$1.binpatch
    Checksum: 3fb8047d Exists: true
  Reading patch net.minecraft.tags.TagLoader$EntryWithSource.binpatch
    Checksum: a9c63163 Exists: true
  Reading patch net.minecraft.tags.TagLoader.binpatch
    Checksum: dc04ecc9 Exists: true
  Reading patch net.minecraft.tags.TagManager$LoadResult.binpatch
    Checksum: 4bfa30f9 Exists: true
  Reading patch net.minecraft.tags.TagManager.binpatch
    Checksum: e57d4b64 Exists: true
  Reading patch net.minecraft.util.datafix.fixes.StructuresBecomeConfiguredFix$Conversion.binpatch
    Checksum: e0e5f627 Exists: true
  Reading patch net.minecraft.util.datafix.fixes.StructuresBecomeConfiguredFix.binpatch
    Checksum: 7acccd46 Exists: true
  Reading patch net.minecraft.world.effect.MobEffect.binpatch
    Checksum: 93fee599 Exists: true
  Reading patch net.minecraft.world.effect.MobEffectInstance$FactorData.binpatch
    Checksum: 754f36ea Exists: true
  Reading patch net.minecraft.world.effect.MobEffectInstance.binpatch
    Checksum: 4728bf20 Exists: true
  Reading patch net.minecraft.world.entity.Entity$1.binpatch
    Checksum: cc61c37 Exists: true
  Reading patch net.minecraft.world.entity.Entity$MoveFunction.binpatch
    Checksum: 1f82703b Exists: true
  Reading patch net.minecraft.world.entity.Entity$MovementEmission.binpatch
    Checksum: 5dc5b4d7 Exists: true
  Reading patch net.minecraft.world.entity.Entity$RemovalReason.binpatch
    Checksum: 786db7bc Exists: true
  Reading patch net.minecraft.world.entity.Entity.binpatch
    Checksum: 9bb33746 Exists: true
  Reading patch net.minecraft.world.entity.EntityType$1.binpatch
    Checksum: c3974c9e Exists: true
  Reading patch net.minecraft.world.entity.EntityType$Builder.binpatch
    Checksum: 4859b23b Exists: true
  Reading patch net.minecraft.world.entity.EntityType$EntityFactory.binpatch
    Checksum: 638ac445 Exists: true
  Reading patch net.minecraft.world.entity.EntityType.binpatch
    Checksum: e82cc71b Exists: true
  Reading patch net.minecraft.world.entity.ExperienceOrb.binpatch
    Checksum: 1497f992 Exists: true
  Reading patch net.minecraft.world.entity.FlyingMob.binpatch
    Checksum: 6475d1aa Exists: true
  Reading patch net.minecraft.world.entity.LightningBolt.binpatch
    Checksum: 8e1ed372 Exists: true
  Reading patch net.minecraft.world.entity.LivingEntity$1.binpatch
    Checksum: f8d64623 Exists: true
  Reading patch net.minecraft.world.entity.LivingEntity$Fallsounds.binpatch
    Checksum: ade5e468 Exists: true
  Reading patch net.minecraft.world.entity.LivingEntity.binpatch
    Checksum: 7ddbeb59 Exists: true
  Reading patch net.minecraft.world.entity.Mob$1.binpatch
    Checksum: 5f392515 Exists: true
  Reading patch net.minecraft.world.entity.Mob.binpatch
    Checksum: 6c24bd02 Exists: true
  Reading patch net.minecraft.world.entity.MobCategory.binpatch
    Checksum: b28be301 Exists: true
  Reading patch net.minecraft.world.entity.Shearable.binpatch
    Checksum: b7a4385d Exists: true
  Reading patch net.minecraft.world.entity.SpawnPlacements$Data.binpatch
    Checksum: fafaf0b5 Exists: true
  Reading patch net.minecraft.world.entity.SpawnPlacements$SpawnPredicate.binpatch
    Checksum: c37535f1 Exists: true
  Reading patch net.minecraft.world.entity.SpawnPlacements$Type.binpatch
    Checksum: c9547264 Exists: true
  Reading patch net.minecraft.world.entity.SpawnPlacements.binpatch
    Checksum: 321e7616 Exists: true
  Reading patch net.minecraft.world.entity.TamableAnimal.binpatch
    Checksum: e40100ab Exists: true
  Reading patch net.minecraft.world.entity.ai.attributes.AttributeSupplier$Builder.binpatch
    Checksum: 1614c9a9 Exists: true
  Reading patch net.minecraft.world.entity.ai.attributes.AttributeSupplier.binpatch
    Checksum: 276055ab Exists: true
  Reading patch net.minecraft.world.entity.ai.attributes.DefaultAttributes.binpatch
    Checksum: 3ef93931 Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.CrossbowAttack$CrossbowState.binpatch
    Checksum: e1f2a2f1 Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.CrossbowAttack.binpatch
    Checksum: 211c41c0 Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.HarvestFarmland.binpatch
    Checksum: 54e1605b Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.Swim.binpatch
    Checksum: 6aebbf2e Exists: true
  Reading patch net.minecraft.world.entity.ai.control.MoveControl$Operation.binpatch
    Checksum: 47048c8b Exists: true
  Reading patch net.minecraft.world.entity.ai.control.MoveControl.binpatch
    Checksum: 6d77ab79 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.BreakDoorGoal.binpatch
    Checksum: 2044f9a5 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.EatBlockGoal.binpatch
    Checksum: b83f154f Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.FloatGoal.binpatch
    Checksum: 25a1003e Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.MeleeAttackGoal.binpatch
    Checksum: 7f8100d Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RangedBowAttackGoal.binpatch
    Checksum: bef9e58a Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RangedCrossbowAttackGoal$CrossbowState.binpatch
    Checksum: ff40b5e9 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RangedCrossbowAttackGoal.binpatch
    Checksum: 712f43e4 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RemoveBlockGoal.binpatch
    Checksum: 24269abb Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RunAroundLikeCrazyGoal.binpatch
    Checksum: eb178e8e Exists: true
  Reading patch net.minecraft.world.entity.ai.navigation.PathNavigation.binpatch
    Checksum: 644f990 Exists: true
  Reading patch net.minecraft.world.entity.ai.navigation.WallClimberNavigation.binpatch
    Checksum: a282e3c8 Exists: true
  Reading patch net.minecraft.world.entity.ai.village.VillageSiege$State.binpatch
    Checksum: ad2a7712 Exists: true
  Reading patch net.minecraft.world.entity.ai.village.VillageSiege.binpatch
    Checksum: 7da21ba8 Exists: true
  Reading patch net.minecraft.world.entity.ai.village.poi.PoiTypes.binpatch
    Checksum: 6b5a1139 Exists: true
  Reading patch net.minecraft.world.entity.animal.Animal.binpatch
    Checksum: 464c843e Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$1.binpatch
    Checksum: e71b27c3 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BaseBeeGoal.binpatch
    Checksum: 9899c878 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeAttackGoal.binpatch
    Checksum: 82a41e18 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeBecomeAngryTargetGoal.binpatch
    Checksum: 6f52dcaa Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeEnterHiveGoal.binpatch
    Checksum: 61671010 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeGoToHiveGoal.binpatch
    Checksum: 1ac0eae7 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeGoToKnownFlowerGoal.binpatch
    Checksum: ec1c1c63 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeGrowCropGoal.binpatch
    Checksum: 28e4764f Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeHurtByOtherGoal.binpatch
    Checksum: ca8b9ebb Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeLocateHiveGoal.binpatch
    Checksum: 4eef81cb Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeLookControl.binpatch
    Checksum: 5893c0b Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeePollinateGoal.binpatch
    Checksum: 1af0de73 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeWanderGoal.binpatch
    Checksum: cfcbfb3f Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee.binpatch
    Checksum: 18538fa1 Exists: true
  Reading patch net.minecraft.world.entity.animal.Cat$CatAvoidEntityGoal.binpatch
    Checksum: a931d1a1 Exists: true
  Reading patch net.minecraft.world.entity.animal.Cat$CatRelaxOnOwnerGoal.binpatch
    Checksum: 157ff053 Exists: true
  Reading patch net.minecraft.world.entity.animal.Cat$CatTemptGoal.binpatch
    Checksum: e5bdf9da Exists: true
  Reading patch net.minecraft.world.entity.animal.Cat.binpatch
    Checksum: d279a755 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$DefendTrustedTargetGoal.binpatch
    Checksum: b92eef6b Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FaceplantGoal.binpatch
    Checksum: 8e2e533d Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxAlertableEntitiesSelector.binpatch
    Checksum: 50b00578 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxBehaviorGoal.binpatch
    Checksum: 86c5fe6f Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxBreedGoal.binpatch
    Checksum: fd6f9172 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxEatBerriesGoal.binpatch
    Checksum: 69f8ed4f Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxFloatGoal.binpatch
    Checksum: 1d841790 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxFollowParentGoal.binpatch
    Checksum: ee421f4 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxGroupData.binpatch
    Checksum: 5e18e148 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxLookAtPlayerGoal.binpatch
    Checksum: 26d7989a Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxLookControl.binpatch
    Checksum: a02effb5 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxMeleeAttackGoal.binpatch
    Checksum: 50e3fa9e Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxMoveControl.binpatch
    Checksum: bb6ad0fe Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxPanicGoal.binpatch
    Checksum: f12dd4bf Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxPounceGoal.binpatch
    Checksum: 70348019 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxSearchForItemsGoal.binpatch
    Checksum: 2e6e1f1 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxStrollThroughVillageGoal.binpatch
    Checksum: 40f6483c Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$PerchAndSearchGoal.binpatch
    Checksum: 6490371e Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$SeekShelterGoal.binpatch
    Checksum: 943ff62d Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$SleepGoal.binpatch
    Checksum: 54b433a2 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$StalkPreyGoal.binpatch
    Checksum: c5d95b2c Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$Type.binpatch
    Checksum: 6cc2cb14 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox.binpatch
    Checksum: 2a0a752f Exists: true
  Reading patch net.minecraft.world.entity.animal.IronGolem$Crackiness.binpatch
    Checksum: 24cdbb38 Exists: true
  Reading patch net.minecraft.world.entity.animal.IronGolem.binpatch
    Checksum: 12a4c479 Exists: true
  Reading patch net.minecraft.world.entity.animal.MushroomCow$MushroomType.binpatch
    Checksum: 80d797dc Exists: true
  Reading patch net.minecraft.world.entity.animal.MushroomCow.binpatch
    Checksum: f8238596 Exists: true
  Reading patch net.minecraft.world.entity.animal.Ocelot$OcelotAvoidEntityGoal.binpatch
    Checksum: 4cc7de41 Exists: true
  Reading patch net.minecraft.world.entity.animal.Ocelot$OcelotTemptGoal.binpatch
    Checksum: 84e728d0 Exists: true
  Reading patch net.minecraft.world.entity.animal.Ocelot.binpatch
    Checksum: 3643ef01 Exists: true
  Reading patch net.minecraft.world.entity.animal.Parrot$1.binpatch
    Checksum: 2e36407c Exists: true
  Reading patch net.minecraft.world.entity.animal.Parrot$ParrotWanderGoal.binpatch
    Checksum: b29bafc5 Exists: true
  Reading patch net.minecraft.world.entity.animal.Parrot.binpatch
    Checksum: ac989905 Exists: true
  Reading patch net.minecraft.world.entity.animal.Pig.binpatch
    Checksum: 9ef0a7f4 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$EvilRabbitAttackGoal.binpatch
    Checksum: b6ae147e Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitAvoidEntityGoal.binpatch
    Checksum: 9979a1f6 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitGroupData.binpatch
    Checksum: 23a1ae12 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitJumpControl.binpatch
    Checksum: fcc10384 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitMoveControl.binpatch
    Checksum: 2c30a3d9 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitPanicGoal.binpatch
    Checksum: b0a1e1ff Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RaidGardenGoal.binpatch
    Checksum: 23c8536e Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit.binpatch
    Checksum: 3f9365c7 Exists: true
  Reading patch net.minecraft.world.entity.animal.Sheep$1.binpatch
    Checksum: f2bc7a5e Exists: true
  Reading patch net.minecraft.world.entity.animal.Sheep$2.binpatch
    Checksum: fd835166 Exists: true
  Reading patch net.minecraft.world.entity.animal.Sheep.binpatch
    Checksum: d8ab554 Exists: true
  Reading patch net.minecraft.world.entity.animal.SnowGolem.binpatch
    Checksum: d80dabfb Exists: true
  Reading patch net.minecraft.world.entity.animal.Wolf$WolfAvoidEntityGoal.binpatch
    Checksum: 2e3ba13f Exists: true
  Reading patch net.minecraft.world.entity.animal.Wolf$WolfPanicGoal.binpatch
    Checksum: d1320623 Exists: true
  Reading patch net.minecraft.world.entity.animal.Wolf.binpatch
    Checksum: 36fb338 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.AbstractHorse$1.binpatch
    Checksum: 560ef0e4 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.AbstractHorse.binpatch
    Checksum: e11e42c4 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.Horse$HorseGroupData.binpatch
    Checksum: 747cdb41 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.Horse.binpatch
    Checksum: 2c86c5 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.SkeletonTrapGoal.binpatch
    Checksum: e4ba995a Exists: true
  Reading patch net.minecraft.world.entity.boss.EnderDragonPart.binpatch
    Checksum: c7d8f402 Exists: true
  Reading patch net.minecraft.world.entity.boss.enderdragon.EnderDragon.binpatch
    Checksum: 6a71a7f2 Exists: true
  Reading patch net.minecraft.world.entity.boss.wither.WitherBoss$WitherDoNothingGoal.binpatch
    Checksum: 97904203 Exists: true
  Reading patch net.minecraft.world.entity.boss.wither.WitherBoss.binpatch
    Checksum: 3e40fac2 Exists: true
  Reading patch net.minecraft.world.entity.decoration.ArmorStand$1.binpatch
    Checksum: 62dfdbf3 Exists: true
  Reading patch net.minecraft.world.entity.decoration.ArmorStand.binpatch
    Checksum: e71febb0 Exists: true
  Reading patch net.minecraft.world.entity.decoration.HangingEntity$1.binpatch
    Checksum: 1cb8d5e2 Exists: true
  Reading patch net.minecraft.world.entity.decoration.HangingEntity.binpatch
    Checksum: f6d11a8c Exists: true
  Reading patch net.minecraft.world.entity.item.FallingBlockEntity.binpatch
    Checksum: 7d68e539 Exists: true
  Reading patch net.minecraft.world.entity.item.ItemEntity.binpatch
    Checksum: fe9075a7 Exists: true
  Reading patch net.minecraft.world.entity.monster.AbstractSkeleton$1.binpatch
    Checksum: c438257f Exists: true
  Reading patch net.minecraft.world.entity.monster.AbstractSkeleton.binpatch
    Checksum: b5034732 Exists: true
  Reading patch net.minecraft.world.entity.monster.Creeper.binpatch
    Checksum: 9ef24827 Exists: true
  Reading patch net.minecraft.world.entity.monster.CrossbowAttackMob.binpatch
    Checksum: 9c847c31 Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan$EndermanFreezeWhenLookedAt.binpatch
    Checksum: 42d88825 Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan$EndermanLeaveBlockGoal.binpatch
    Checksum: 471a2178 Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan$EndermanLookForPlayerGoal.binpatch
    Checksum: 94b6634d Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan$EndermanTakeBlockGoal.binpatch
    Checksum: b341b632 Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan.binpatch
    Checksum: b610181f Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker$EvokerAttackSpellGoal.binpatch
    Checksum: 7d9fa490 Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker$EvokerCastingSpellGoal.binpatch
    Checksum: 90ebb51c Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker$EvokerSummonSpellGoal.binpatch
    Checksum: 417cceb9 Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker$EvokerWololoSpellGoal.binpatch
    Checksum: aa0184e9 Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker.binpatch
    Checksum: 61cf94f1 Exists: true
  Reading patch net.minecraft.world.entity.monster.Illusioner$IllusionerBlindnessSpellGoal.binpatch
    Checksum: e8c79035 Exists: true
  Reading patch net.minecraft.world.entity.monster.Illusioner$IllusionerMirrorSpellGoal.binpatch
    Checksum: 4c2684dd Exists: true
  Reading patch net.minecraft.world.entity.monster.Illusioner.binpatch
    Checksum: 250d9cea Exists: true
  Reading patch net.minecraft.world.entity.monster.MagmaCube.binpatch
    Checksum: a2c22f67 Exists: true
  Reading patch net.minecraft.world.entity.monster.Monster.binpatch
    Checksum: da370866 Exists: true
  Reading patch net.minecraft.world.entity.monster.Pillager.binpatch
    Checksum: ad717af0 Exists: true
  Reading patch net.minecraft.world.entity.monster.Ravager$RavagerMeleeAttackGoal.binpatch
    Checksum: 365325c0 Exists: true
  Reading patch net.minecraft.world.entity.monster.Ravager$RavagerNavigation.binpatch
    Checksum: 718b6963 Exists: true
  Reading patch net.minecraft.world.entity.monster.Ravager$RavagerNodeEvaluator.binpatch
    Checksum: 25a85494 Exists: true
  Reading patch net.minecraft.world.entity.monster.Ravager.binpatch
    Checksum: d35630bd Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerAttackGoal.binpatch
    Checksum: 8c984e60 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerBodyRotationControl.binpatch
    Checksum: 52d1c40c Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerDefenseAttackGoal.binpatch
    Checksum: 62256f5 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerLookControl.binpatch
    Checksum: 5d4a1b07 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerNearestAttackGoal.binpatch
    Checksum: 80a6bd94 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerPeekGoal.binpatch
    Checksum: 19bfb74b Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker.binpatch
    Checksum: dfaf0b88 Exists: true
  Reading patch net.minecraft.world.entity.monster.Silverfish$SilverfishMergeWithStoneGoal.binpatch
    Checksum: 6b66f548 Exists: true
  Reading patch net.minecraft.world.entity.monster.Silverfish$SilverfishWakeUpFriendsGoal.binpatch
    Checksum: 69bfaa0d Exists: true
  Reading patch net.minecraft.world.entity.monster.Silverfish.binpatch
    Checksum: d19ffa7d Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeAttackGoal.binpatch
    Checksum: d7604109 Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeFloatGoal.binpatch
    Checksum: 90323cc4 Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeKeepOnJumpingGoal.binpatch
    Checksum: bf2876fd Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeMoveControl.binpatch
    Checksum: 11f12c6d Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeRandomDirectionGoal.binpatch
    Checksum: 30715b8d Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime.binpatch
    Checksum: 3466463e Exists: true
  Reading patch net.minecraft.world.entity.monster.Spider$SpiderAttackGoal.binpatch
    Checksum: d7cab6ee Exists: true
  Reading patch net.minecraft.world.entity.monster.Spider$SpiderEffectsGroupData.binpatch
    Checksum: 84a43f31 Exists: true
  Reading patch net.minecraft.world.entity.monster.Spider$SpiderTargetGoal.binpatch
    Checksum: 9f5cb5fa Exists: true
  Reading patch net.minecraft.world.entity.monster.Spider.binpatch
    Checksum: 376da1a8 Exists: true
  Reading patch net.minecraft.world.entity.monster.Zombie$ZombieAttackTurtleEggGoal.binpatch
    Checksum: ce7ef333 Exists: true
  Reading patch net.minecraft.world.entity.monster.Zombie$ZombieGroupData.binpatch
    Checksum: 4fe89f88 Exists: true
  Reading patch net.minecraft.world.entity.monster.Zombie.binpatch
    Checksum: 23cce8b5 Exists: true
  Reading patch net.minecraft.world.entity.monster.ZombieVillager.binpatch
    Checksum: dfdd9a82 Exists: true
  Reading patch net.minecraft.world.entity.monster.hoglin.Hoglin.binpatch
    Checksum: e1a5292d Exists: true
  Reading patch net.minecraft.world.entity.monster.piglin.AbstractPiglin.binpatch
    Checksum: 1a1a0b2b Exists: true
  Reading patch net.minecraft.world.entity.monster.piglin.Piglin.binpatch
    Checksum: 1fa74c7 Exists: true
  Reading patch net.minecraft.world.entity.monster.piglin.PiglinAi.binpatch
    Checksum: 22fd8a86 Exists: true
  Reading patch net.minecraft.world.entity.monster.piglin.StopHoldingItemIfNoLongerAdmiring.binpatch
    Checksum: 8aa25730 Exists: true
  Reading patch net.minecraft.world.entity.npc.AbstractVillager.binpatch
    Checksum: 1c1f2aa8 Exists: true
  Reading patch net.minecraft.world.entity.npc.CatSpawner.binpatch
    Checksum: 19ac5639 Exists: true
  Reading patch net.minecraft.world.entity.npc.Villager.binpatch
    Checksum: 66c5c5b5 Exists: true
  Reading patch net.minecraft.world.entity.player.Inventory.binpatch
    Checksum: 1624ac4f Exists: true
  Reading patch net.minecraft.world.entity.player.Player$1.binpatch
    Checksum: c314c5ab Exists: true
  Reading patch net.minecraft.world.entity.player.Player$BedSleepingProblem.binpatch
    Checksum: 829cb54e Exists: true
  Reading patch net.minecraft.world.entity.player.Player.binpatch
    Checksum: d48b8d75 Exists: true
  Reading patch net.minecraft.world.entity.projectile.AbstractArrow$1.binpatch
    Checksum: 680fe19e Exists: true
  Reading patch net.minecraft.world.entity.projectile.AbstractArrow$Pickup.binpatch
    Checksum: 5ad6b0ee Exists: true
  Reading patch net.minecraft.world.entity.projectile.AbstractArrow.binpatch
    Checksum: 42cc64dd Exists: true
  Reading patch net.minecraft.world.entity.projectile.AbstractHurtingProjectile.binpatch
    Checksum: f3797ee5 Exists: true
  Reading patch net.minecraft.world.entity.projectile.FireworkRocketEntity.binpatch
    Checksum: 1b42b2ac Exists: true
  Reading patch net.minecraft.world.entity.projectile.FishingHook$1.binpatch
    Checksum: 6acbf1ef Exists: true
  Reading patch net.minecraft.world.entity.projectile.FishingHook$FishHookState.binpatch
    Checksum: cbd088ea Exists: true
  Reading patch net.minecraft.world.entity.projectile.FishingHook$OpenWaterType.binpatch
    Checksum: 9ac08c7d Exists: true
  Reading patch net.minecraft.world.entity.projectile.FishingHook.binpatch
    Checksum: fc998da2 Exists: true
  Reading patch net.minecraft.world.entity.projectile.LargeFireball.binpatch
    Checksum: 8bcc2dd1 Exists: true
  Reading patch net.minecraft.world.entity.projectile.LlamaSpit.binpatch
    Checksum: f80887de Exists: true
  Reading patch net.minecraft.world.entity.projectile.Projectile.binpatch
    Checksum: df0a0667 Exists: true
  Reading patch net.minecraft.world.entity.projectile.ProjectileUtil.binpatch
    Checksum: 9eb99972 Exists: true
  Reading patch net.minecraft.world.entity.projectile.ShulkerBullet.binpatch
    Checksum: d9eab2c9 Exists: true
  Reading patch net.minecraft.world.entity.projectile.SmallFireball.binpatch
    Checksum: 2598dbd2 Exists: true
  Reading patch net.minecraft.world.entity.projectile.ThrowableProjectile.binpatch
    Checksum: f5b313e3 Exists: true
  Reading patch net.minecraft.world.entity.projectile.ThrownEnderpearl.binpatch
    Checksum: f061f626 Exists: true
  Reading patch net.minecraft.world.entity.projectile.WitherSkull.binpatch
    Checksum: ed7a9c70 Exists: true
  Reading patch net.minecraft.world.entity.raid.Raid$1.binpatch
    Checksum: 598f2f2b Exists: true
  Reading patch net.minecraft.world.entity.raid.Raid$RaidStatus.binpatch
    Checksum: 55ffe3c7 Exists: true
  Reading patch net.minecraft.world.entity.raid.Raid$RaiderType.binpatch
    Checksum: fa26897d Exists: true
  Reading patch net.minecraft.world.entity.raid.Raid.binpatch
    Checksum: ad5fd159 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.AbstractMinecart$1.binpatch
    Checksum: ca1a8ef0 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.AbstractMinecart$Type.binpatch
    Checksum: 4418adbc Exists: true
  Reading patch net.minecraft.world.entity.vehicle.AbstractMinecart.binpatch
    Checksum: 738cbf29 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.AbstractMinecartContainer.binpatch
    Checksum: 80a51bfd Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Boat$1.binpatch
    Checksum: c8ca88fb Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Boat$Status.binpatch
    Checksum: 592b7a43 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Boat$Type.binpatch
    Checksum: 8e9d2d9c Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Boat.binpatch
    Checksum: b00e9335 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.ChestBoat$1.binpatch
    Checksum: bb520095 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.ChestBoat.binpatch
    Checksum: 7b27fe5d Exists: true
  Reading patch net.minecraft.world.entity.vehicle.ContainerEntity$1.binpatch
    Checksum: 54b35977 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.ContainerEntity.binpatch
    Checksum: 370a640b Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Minecart.binpatch
    Checksum: 26804e31 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartCommandBlock$MinecartCommandBase.binpatch
    Checksum: b3a326d5 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartCommandBlock.binpatch
    Checksum: 6b6c5736 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartFurnace.binpatch
    Checksum: 4ff4d8cc Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartSpawner$1.binpatch
    Checksum: ab0029c1 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartSpawner.binpatch
    Checksum: ec75ad7b Exists: true
  Reading patch net.minecraft.world.food.FoodData.binpatch
    Checksum: c93b7edc Exists: true
  Reading patch net.minecraft.world.food.FoodProperties$Builder.binpatch
    Checksum: 63c73127 Exists: true
  Reading patch net.minecraft.world.food.FoodProperties.binpatch
    Checksum: 6daccda9 Exists: true
  Reading patch net.minecraft.world.inventory.AbstractContainerMenu$1.binpatch
    Checksum: 2cb73ad7 Exists: true
  Reading patch net.minecraft.world.inventory.AbstractContainerMenu.binpatch
    Checksum: 4327d901 Exists: true
  Reading patch net.minecraft.world.inventory.AbstractFurnaceMenu.binpatch
    Checksum: 89e6f93 Exists: true
  Reading patch net.minecraft.world.inventory.AnvilMenu$1.binpatch
    Checksum: c37af9d7 Exists: true
  Reading patch net.minecraft.world.inventory.AnvilMenu.binpatch
    Checksum: a87197f1 Exists: true
  Reading patch net.minecraft.world.inventory.BeaconMenu$1.binpatch
    Checksum: 39331d16 Exists: true
  Reading patch net.minecraft.world.inventory.BeaconMenu$PaymentSlot.binpatch
    Checksum: af9a57c7 Exists: true
  Reading patch net.minecraft.world.inventory.BeaconMenu.binpatch
    Checksum: 33fa4988 Exists: true
  Reading patch net.minecraft.world.inventory.BrewingStandMenu$FuelSlot.binpatch
    Checksum: 9a7831a5 Exists: true
  Reading patch net.minecraft.world.inventory.BrewingStandMenu$IngredientsSlot.binpatch
    Checksum: 44b6030a Exists: true
  Reading patch net.minecraft.world.inventory.BrewingStandMenu$PotionSlot.binpatch
    Checksum: 4d018536 Exists: true
  Reading patch net.minecraft.world.inventory.BrewingStandMenu.binpatch
    Checksum: 8736bb61 Exists: true
  Reading patch net.minecraft.world.inventory.EnchantmentMenu$1.binpatch
    Checksum: 75afd633 Exists: true
  Reading patch net.minecraft.world.inventory.EnchantmentMenu$2.binpatch
    Checksum: 26c8494d Exists: true
  Reading patch net.minecraft.world.inventory.EnchantmentMenu$3.binpatch
    Checksum: 17088ac4 Exists: true
  Reading patch net.minecraft.world.inventory.EnchantmentMenu.binpatch
    Checksum: 37d42ac3 Exists: true
  Reading patch net.minecraft.world.inventory.FurnaceResultSlot.binpatch
    Checksum: 42c18970 Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu$1.binpatch
    Checksum: f779d44c Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu$2.binpatch
    Checksum: bd2aa096 Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu$3.binpatch
    Checksum: b552a09d Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu$4.binpatch
    Checksum: f2822cce Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu.binpatch
    Checksum: 5235003b Exists: true
  Reading patch net.minecraft.world.inventory.InventoryMenu$1.binpatch
    Checksum: be68a946 Exists: true
  Reading patch net.minecraft.world.inventory.InventoryMenu$2.binpatch
    Checksum: eeb7b30e Exists: true
  Reading patch net.minecraft.world.inventory.InventoryMenu.binpatch
    Checksum: 343be611 Exists: true
  Reading patch net.minecraft.world.inventory.MenuType$MenuSupplier.binpatch
    Checksum: f103b903 Exists: true
  Reading patch net.minecraft.world.inventory.MenuType.binpatch
    Checksum: 97857457 Exists: true
  Reading patch net.minecraft.world.inventory.RecipeBookMenu.binpatch
    Checksum: d4ec624c Exists: true
  Reading patch net.minecraft.world.inventory.RecipeBookType.binpatch
    Checksum: 86b940fe Exists: true
  Reading patch net.minecraft.world.inventory.ResultSlot.binpatch
    Checksum: d96fccb Exists: true
  Reading patch net.minecraft.world.inventory.Slot.binpatch
    Checksum: 68c08321 Exists: true
  Reading patch net.minecraft.world.item.ArmorItem$1.binpatch
    Checksum: e69a0ad3 Exists: true
  Reading patch net.minecraft.world.item.ArmorItem.binpatch
    Checksum: d86ec2f1 Exists: true
  Reading patch net.minecraft.world.item.ArrowItem.binpatch
    Checksum: a7dd7c61 Exists: true
  Reading patch net.minecraft.world.item.AxeItem.binpatch
    Checksum: e00fae71 Exists: true
  Reading patch net.minecraft.world.item.BannerItem.binpatch
    Checksum: 8297ba20 Exists: true
  Reading patch net.minecraft.world.item.BlockItem.binpatch
    Checksum: b57a3d4d Exists: true
  Reading patch net.minecraft.world.item.BoneMealItem.binpatch
    Checksum: 2e17d9c Exists: true
  Reading patch net.minecraft.world.item.BowItem.binpatch
    Checksum: 7a7ddc1f Exists: true
  Reading patch net.minecraft.world.item.BucketItem.binpatch
    Checksum: 614d5436 Exists: true
  Reading patch net.minecraft.world.item.ChorusFruitItem.binpatch
    Checksum: 2e460ce Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$1.binpatch
    Checksum: c4abdca5 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$10.binpatch
    Checksum: 52474bb3 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$11.binpatch
    Checksum: 679376bc Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$12.binpatch
    Checksum: df16ddaf Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$2.binpatch
    Checksum: b978dca9 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$3.binpatch
    Checksum: 21bfd698 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$4.binpatch
    Checksum: e1ddcd2 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$5.binpatch
    Checksum: 4ec6d6bf Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$6.binpatch
    Checksum: aee8d6e9 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$7.binpatch
    Checksum: 9314d724 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$8.binpatch
    Checksum: 4112d773 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$9.binpatch
    Checksum: 2aebd770 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab.binpatch
    Checksum: d5dd537e Exists: true
  Reading patch net.minecraft.world.item.CrossbowItem.binpatch
    Checksum: e9647917 Exists: true
  Reading patch net.minecraft.world.item.DiggerItem.binpatch
    Checksum: 29ee59b4 Exists: true
  Reading patch net.minecraft.world.item.DyeColor.binpatch
    Checksum: 4442318b Exists: true
  Reading patch net.minecraft.world.item.DyeableHorseArmorItem.binpatch
    Checksum: 484fe1a2 Exists: true
  Reading patch net.minecraft.world.item.ElytraItem.binpatch
    Checksum: e580092c Exists: true
  Reading patch net.minecraft.world.item.EnchantedBookItem.binpatch
    Checksum: ece3456c Exists: true
  Reading patch net.minecraft.world.item.FishingRodItem.binpatch
    Checksum: ee4e21c2 Exists: true
  Reading patch net.minecraft.world.item.HoeItem.binpatch
    Checksum: 23e25f40 Exists: true
  Reading patch net.minecraft.world.item.HorseArmorItem.binpatch
    Checksum: ab19be88 Exists: true
  Reading patch net.minecraft.world.item.Item$1.binpatch
    Checksum: ff97b617 Exists: true
  Reading patch net.minecraft.world.item.Item$Properties.binpatch
    Checksum: a6ed34a2 Exists: true
  Reading patch net.minecraft.world.item.Item.binpatch
    Checksum: 5837649a Exists: true
  Reading patch net.minecraft.world.item.ItemStack$TooltipPart.binpatch
    Checksum: 353aec7 Exists: true
  Reading patch net.minecraft.world.item.ItemStack.binpatch
    Checksum: 6bf7dcc1 Exists: true
  Reading patch net.minecraft.world.item.Items$1.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.world.item.Items.binpatch
    Checksum: dea9d6ba Exists: true
  Reading patch net.minecraft.world.item.MapItem.binpatch
    Checksum: 5e388fc7 Exists: true
  Reading patch net.minecraft.world.item.MilkBucketItem.binpatch
    Checksum: f81b6e87 Exists: true
  Reading patch net.minecraft.world.item.MinecartItem$1.binpatch
    Checksum: 4efea108 Exists: true
  Reading patch net.minecraft.world.item.MinecartItem.binpatch
    Checksum: 298032e1 Exists: true
  Reading patch net.minecraft.world.item.MobBucketItem.binpatch
    Checksum: cd3cb8c Exists: true
  Reading patch net.minecraft.world.item.PickaxeItem.binpatch
    Checksum: d48d1a56 Exists: true
  Reading patch net.minecraft.world.item.PotionItem.binpatch
    Checksum: d6b82622 Exists: true
  Reading patch net.minecraft.world.item.Rarity.binpatch
    Checksum: e41c893e Exists: true
  Reading patch net.minecraft.world.item.RecordItem.binpatch
    Checksum: ae5ab529 Exists: true
  Reading patch net.minecraft.world.item.ShearsItem.binpatch
    Checksum: 81fa469c Exists: true
  Reading patch net.minecraft.world.item.ShieldItem.binpatch
    Checksum: bdf06885 Exists: true
  Reading patch net.minecraft.world.item.ShovelItem.binpatch
    Checksum: 9abb522b Exists: true
  Reading patch net.minecraft.world.item.SpawnEggItem.binpatch
    Checksum: aae5df3c Exists: true
  Reading patch net.minecraft.world.item.StandingAndWallBlockItem.binpatch
    Checksum: d58505b Exists: true
  Reading patch net.minecraft.world.item.SuspiciousStewItem.binpatch
    Checksum: eb1d0f0d Exists: true
  Reading patch net.minecraft.world.item.SwordItem.binpatch
    Checksum: e77ca362 Exists: true
  Reading patch net.minecraft.world.item.Tier.binpatch
    Checksum: b65c42a8 Exists: true
  Reading patch net.minecraft.world.item.Tiers.binpatch
    Checksum: 9e05de0a Exists: true
  Reading patch net.minecraft.world.item.TippedArrowItem.binpatch
    Checksum: cccd196e Exists: true
  Reading patch net.minecraft.world.item.alchemy.Potion.binpatch
    Checksum: 27f4f303 Exists: true
  Reading patch net.minecraft.world.item.alchemy.PotionBrewing$Mix.binpatch
    Checksum: 23732c8b Exists: true
  Reading patch net.minecraft.world.item.alchemy.PotionBrewing.binpatch
    Checksum: 9c78700e Exists: true
  Reading patch net.minecraft.world.item.crafting.BannerDuplicateRecipe.binpatch
    Checksum: 85c0136d Exists: true
  Reading patch net.minecraft.world.item.crafting.BookCloningRecipe.binpatch
    Checksum: 4aa36909 Exists: true
  Reading patch net.minecraft.world.item.crafting.Ingredient$ItemValue.binpatch
    Checksum: 63e4ece8 Exists: true
  Reading patch net.minecraft.world.item.crafting.Ingredient$TagValue.binpatch
    Checksum: af6e0607 Exists: true
  Reading patch net.minecraft.world.item.crafting.Ingredient$Value.binpatch
    Checksum: c5cb8557 Exists: true
  Reading patch net.minecraft.world.item.crafting.Ingredient.binpatch
    Checksum: 4f361fbb Exists: true
  Reading patch net.minecraft.world.item.crafting.Recipe.binpatch
    Checksum: aeb933b2 Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeManager$1.binpatch
    Checksum: f0aa834b Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeManager$CachedCheck.binpatch
    Checksum: 1729d6ed Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeManager.binpatch
    Checksum: e0a44623 Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeSerializer.binpatch
    Checksum: fc24e932 Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeType$1.binpatch
    Checksum: ec6be044 Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeType$2.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.world.item.crafting.RecipeType.binpatch
    Checksum: 82d909ab Exists: true
  Reading patch net.minecraft.world.item.crafting.RepairItemRecipe.binpatch
    Checksum: 3c1cf583 Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapedRecipe$Serializer.binpatch
    Checksum: 486ca58b Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapedRecipe.binpatch
    Checksum: ecb7ee6a Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapelessRecipe$Serializer.binpatch
    Checksum: 23f74819 Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapelessRecipe.binpatch
    Checksum: 7ca99410 Exists: true
  Reading patch net.minecraft.world.item.crafting.ShulkerBoxColoring.binpatch
    Checksum: cdf6e6d4 Exists: true
  Reading patch net.minecraft.world.item.crafting.SimpleCookingSerializer$CookieBaker.binpatch
    Checksum: f9002dfc Exists: true
  Reading patch net.minecraft.world.item.crafting.SimpleCookingSerializer.binpatch
    Checksum: 57d76d33 Exists: true
  Reading patch net.minecraft.world.item.crafting.UpgradeRecipe$Serializer.binpatch
    Checksum: 6162796e Exists: true
  Reading patch net.minecraft.world.item.crafting.UpgradeRecipe.binpatch
    Checksum: d62b51e9 Exists: true
  Reading patch net.minecraft.world.item.enchantment.DiggingEnchantment.binpatch
    Checksum: df7ef921 Exists: true
  Reading patch net.minecraft.world.item.enchantment.Enchantment$Rarity.binpatch
    Checksum: 19d89bb3 Exists: true
  Reading patch net.minecraft.world.item.enchantment.Enchantment.binpatch
    Checksum: 4c7e3849 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$1.binpatch
    Checksum: 3525bb72 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$10.binpatch
    Checksum: edb1bf03 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$11.binpatch
    Checksum: b728bb74 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$12.binpatch
    Checksum: d074f97a Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$13.binpatch
    Checksum: 3c95bdcc Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$14.binpatch
    Checksum: e7211c20 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$2.binpatch
    Checksum: 1ffd0640 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$3.binpatch
    Checksum: 3b070654 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$4.binpatch
    Checksum: 8dd506b7 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$5.binpatch
    Checksum: a28306b5 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$6.binpatch
    Checksum: 56b2bc0d Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$7.binpatch
    Checksum: 9547bc83 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$8.binpatch
    Checksum: bab7be2a Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$9.binpatch
    Checksum: a5e9bd17 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory.binpatch
    Checksum: 3298f2e6 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentHelper$EnchantmentVisitor.binpatch
    Checksum: 233e8f38 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentHelper.binpatch
    Checksum: 741920c4 Exists: true
  Reading patch net.minecraft.world.item.enchantment.FrostWalkerEnchantment.binpatch
    Checksum: 81eb8787 Exists: true
  Reading patch net.minecraft.world.item.trading.MerchantOffer.binpatch
    Checksum: feba47cd Exists: true
  Reading patch net.minecraft.world.level.BaseSpawner.binpatch
    Checksum: e2250524 Exists: true
  Reading patch net.minecraft.world.level.BlockAndTintGetter.binpatch
    Checksum: 7f83c151 Exists: true
  Reading patch net.minecraft.world.level.BlockGetter.binpatch
    Checksum: 68fcfb10 Exists: true
  Reading patch net.minecraft.world.level.ClipContext$Block.binpatch
    Checksum: b33cd9d6 Exists: true
  Reading patch net.minecraft.world.level.ClipContext$Fluid.binpatch
    Checksum: 75ff8a32 Exists: true
  Reading patch net.minecraft.world.level.ClipContext$ShapeGetter.binpatch
    Checksum: 270ea4fc Exists: true
  Reading patch net.minecraft.world.level.ClipContext.binpatch
    Checksum: fc36e798 Exists: true
  Reading patch net.minecraft.world.level.DataPackConfig.binpatch
    Checksum: 78907e24 Exists: true
  Reading patch net.minecraft.world.level.Explosion$BlockInteraction.binpatch
    Checksum: 7516672e Exists: true
  Reading patch net.minecraft.world.level.Explosion.binpatch
    Checksum: 84b9d2d7 Exists: true
  Reading patch net.minecraft.world.level.ExplosionDamageCalculator.binpatch
    Checksum: faefb47e Exists: true
  Reading patch net.minecraft.world.level.ForcedChunksSavedData.binpatch
    Checksum: af9d6e36 Exists: true
  Reading patch net.minecraft.world.level.Level$1.binpatch
    Checksum: 87f54385 Exists: true
  Reading patch net.minecraft.world.level.Level.binpatch
    Checksum: cf600597 Exists: true
  Reading patch net.minecraft.world.level.LevelReader.binpatch
    Checksum: 9d51130c Exists: true
  Reading patch net.minecraft.world.level.LevelSettings.binpatch
    Checksum: 1026ecb4 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$1.binpatch
    Checksum: 2932e7d7 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$AfterSpawnCallback.binpatch
    Checksum: c3368c49 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$ChunkGetter.binpatch
    Checksum: 3a5ba1cc Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$SpawnPredicate.binpatch
    Checksum: 294d087 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$SpawnState.binpatch
    Checksum: 1e5c3b35 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner.binpatch
    Checksum: f0740dd Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$1.binpatch
    Checksum: d1df2fe4 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$BiomeBuilder.binpatch
    Checksum: 8509259 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$ClimateSettings.binpatch
    Checksum: 87d570a2 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$Precipitation.binpatch
    Checksum: 5fbf02e1 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$TemperatureModifier$1.binpatch
    Checksum: e3cd2c3 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$TemperatureModifier$2.binpatch
    Checksum: 60f67469 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$TemperatureModifier.binpatch
    Checksum: bc055c2f Exists: true
  Reading patch net.minecraft.world.level.biome.Biome.binpatch
    Checksum: 53b547a2 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeGenerationSettings$Builder.binpatch
    Checksum: 431eb4e8 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeGenerationSettings.binpatch
    Checksum: 9b6fa5b2 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSource.binpatch
    Checksum: dc4fe9a2 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$Builder.binpatch
    Checksum: 25b2ffb0 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier$1.binpatch
    Checksum: a806d6f9 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier$2.binpatch
    Checksum: eeeddc90 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier$3.binpatch
    Checksum: b420417e Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier$ColorModifier.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier.binpatch
    Checksum: ffa9c727 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects.binpatch
    Checksum: 1b5e7eb8 Exists: true
  Reading patch net.minecraft.world.level.biome.MobSpawnSettings$Builder.binpatch
    Checksum: ca0b1b77 Exists: true
  Reading patch net.minecraft.world.level.biome.MobSpawnSettings$MobSpawnCost.binpatch
    Checksum: a4a70884 Exists: true
  Reading patch net.minecraft.world.level.biome.MobSpawnSettings$SpawnerData.binpatch
    Checksum: 821eb308 Exists: true
  Reading patch net.minecraft.world.level.biome.MobSpawnSettings.binpatch
    Checksum: 7e4d01b3 Exists: true
  Reading patch net.minecraft.world.level.block.BambooBlock.binpatch
    Checksum: 878552b9 Exists: true
  Reading patch net.minecraft.world.level.block.BambooSaplingBlock.binpatch
    Checksum: b23885cc Exists: true
  Reading patch net.minecraft.world.level.block.BaseFireBlock.binpatch
    Checksum: b3af0936 Exists: true
  Reading patch net.minecraft.world.level.block.BaseRailBlock$1.binpatch
    Checksum: 7c3df0e5 Exists: true
  Reading patch net.minecraft.world.level.block.BaseRailBlock.binpatch
    Checksum: 567b1a55 Exists: true
  Reading patch net.minecraft.world.level.block.BeehiveBlock.binpatch
    Checksum: 3f7bf49 Exists: true
  Reading patch net.minecraft.world.level.block.Block$1.binpatch
    Checksum: 8c33cf0c Exists: true
  Reading patch net.minecraft.world.level.block.Block$2.binpatch
    Checksum: 777df803 Exists: true
  Reading patch net.minecraft.world.level.block.Block$BlockStatePairKey.binpatch
    Checksum: 22ad4586 Exists: true
  Reading patch net.minecraft.world.level.block.Block.binpatch
    Checksum: 2792ed74 Exists: true
  Reading patch net.minecraft.world.level.block.Blocks.binpatch
    Checksum: 900eb26e Exists: true
  Reading patch net.minecraft.world.level.block.BucketPickup.binpatch
    Checksum: b7459b89 Exists: true
  Reading patch net.minecraft.world.level.block.BushBlock.binpatch
    Checksum: 7f8290ca Exists: true
  Reading patch net.minecraft.world.level.block.CactusBlock.binpatch
    Checksum: 6eb4683d Exists: true
  Reading patch net.minecraft.world.level.block.CampfireBlock.binpatch
    Checksum: dc01ff23 Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$1.binpatch
    Checksum: 75b9f7df Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$2$1.binpatch
    Checksum: ca6d5263 Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$2.binpatch
    Checksum: f5347585 Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$3.binpatch
    Checksum: f44914c8 Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$4.binpatch
    Checksum: c106bd71 Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock.binpatch
    Checksum: 843b9645 Exists: true
  Reading patch net.minecraft.world.level.block.ChorusFlowerBlock.binpatch
    Checksum: 1409a5d7 Exists: true
  Reading patch net.minecraft.world.level.block.CocoaBlock$1.binpatch
    Checksum: 6fb9bd24 Exists: true
  Reading patch net.minecraft.world.level.block.CocoaBlock.binpatch
    Checksum: 7308d9c3 Exists: true
  Reading patch net.minecraft.world.level.block.ComparatorBlock.binpatch
    Checksum: f07ef2ee Exists: true
  Reading patch net.minecraft.world.level.block.ConcretePowderBlock.binpatch
    Checksum: beeff2ae Exists: true
  Reading patch net.minecraft.world.level.block.CoralBlock.binpatch
    Checksum: b2295f0e Exists: true
  Reading patch net.minecraft.world.level.block.CropBlock.binpatch
    Checksum: 53d69f44 Exists: true
  Reading patch net.minecraft.world.level.block.DeadBushBlock.binpatch
    Checksum: c55b6edc Exists: true
  Reading patch net.minecraft.world.level.block.DetectorRailBlock$1.binpatch
    Checksum: 2cf23df Exists: true
  Reading patch net.minecraft.world.level.block.DetectorRailBlock.binpatch
    Checksum: cdd2b7c8 Exists: true
  Reading patch net.minecraft.world.level.block.DiodeBlock.binpatch
    Checksum: 7e166edc Exists: true
  Reading patch net.minecraft.world.level.block.DoublePlantBlock.binpatch
    Checksum: 2dfd9ce2 Exists: true
  Reading patch net.minecraft.world.level.block.DropExperienceBlock.binpatch
    Checksum: a9835611 Exists: true
  Reading patch net.minecraft.world.level.block.DropperBlock.binpatch
    Checksum: 3071c41b Exists: true
  Reading patch net.minecraft.world.level.block.EnchantmentTableBlock.binpatch
    Checksum: 3737fd36 Exists: true
  Reading patch net.minecraft.world.level.block.FarmBlock.binpatch
    Checksum: 4d42292c Exists: true
  Reading patch net.minecraft.world.level.block.FireBlock.binpatch
    Checksum: 1b0acebb Exists: true
  Reading patch net.minecraft.world.level.block.FlowerPotBlock.binpatch
    Checksum: d8e63e1d Exists: true
  Reading patch net.minecraft.world.level.block.GrowingPlantHeadBlock.binpatch
    Checksum: dc6724e9 Exists: true
  Reading patch net.minecraft.world.level.block.LeavesBlock.binpatch
    Checksum: 2df4d413 Exists: true
  Reading patch net.minecraft.world.level.block.LiquidBlock.binpatch
    Checksum: 6f934c1d Exists: true
  Reading patch net.minecraft.world.level.block.MagmaBlock.binpatch
    Checksum: 949cb524 Exists: true
  Reading patch net.minecraft.world.level.block.MushroomBlock.binpatch
    Checksum: 4b930172 Exists: true
  Reading patch net.minecraft.world.level.block.NetherWartBlock.binpatch
    Checksum: f51a0227 Exists: true
  Reading patch net.minecraft.world.level.block.NoteBlock.binpatch
    Checksum: fa70c0cf Exists: true
  Reading patch net.minecraft.world.level.block.PowderSnowBlock.binpatch
    Checksum: ea54636d Exists: true
  Reading patch net.minecraft.world.level.block.PoweredRailBlock$1.binpatch
    Checksum: a75122db Exists: true
  Reading patch net.minecraft.world.level.block.PoweredRailBlock.binpatch
    Checksum: 41aadb34 Exists: true
  Reading patch net.minecraft.world.level.block.PumpkinBlock.binpatch
    Checksum: 34babdc9 Exists: true
  Reading patch net.minecraft.world.level.block.RailBlock$1.binpatch
    Checksum: 9383194f Exists: true
  Reading patch net.minecraft.world.level.block.RailBlock.binpatch
    Checksum: 1635d89f Exists: true
  Reading patch net.minecraft.world.level.block.RailState$1.binpatch
    Checksum: 90ae36bf Exists: true
  Reading patch net.minecraft.world.level.block.RailState.binpatch
    Checksum: 473d2011 Exists: true
  Reading patch net.minecraft.world.level.block.RedStoneOreBlock.binpatch
    Checksum: e5d97a4b Exists: true
  Reading patch net.minecraft.world.level.block.RedStoneWireBlock$1.binpatch
    Checksum: 2a93ad6d Exists: true
  Reading patch net.minecraft.world.level.block.RedStoneWireBlock.binpatch
    Checksum: a85f94db Exists: true
  Reading patch net.minecraft.world.level.block.SaplingBlock.binpatch
    Checksum: 851ca67f Exists: true
  Reading patch net.minecraft.world.level.block.SculkCatalystBlock.binpatch
    Checksum: b5623d6e Exists: true
  Reading patch net.minecraft.world.level.block.SculkSensorBlock.binpatch
    Checksum: 833af5ee Exists: true
  Reading patch net.minecraft.world.level.block.SculkShriekerBlock.binpatch
    Checksum: 9dccd439 Exists: true
  Reading patch net.minecraft.world.level.block.SeagrassBlock.binpatch
    Checksum: 7ca86a80 Exists: true
  Reading patch net.minecraft.world.level.block.SoundType.binpatch
    Checksum: eaf7d6fc Exists: true
  Reading patch net.minecraft.world.level.block.SpawnerBlock.binpatch
    Checksum: 9abdb373 Exists: true
  Reading patch net.minecraft.world.level.block.SpongeBlock.binpatch
    Checksum: 9c0295af Exists: true
  Reading patch net.minecraft.world.level.block.SpreadingSnowyDirtBlock.binpatch
    Checksum: 781f02f2 Exists: true
  Reading patch net.minecraft.world.level.block.StairBlock$1.binpatch
    Checksum: 28f532e Exists: true
  Reading patch net.minecraft.world.level.block.StairBlock.binpatch
    Checksum: 33155d04 Exists: true
  Reading patch net.minecraft.world.level.block.StemBlock.binpatch
    Checksum: 2c7a0c48 Exists: true
  Reading patch net.minecraft.world.level.block.SugarCaneBlock.binpatch
    Checksum: 1b157b23 Exists: true
  Reading patch net.minecraft.world.level.block.SweetBerryBushBlock.binpatch
    Checksum: dc478154 Exists: true
  Reading patch net.minecraft.world.level.block.TallGrassBlock.binpatch
    Checksum: 8f179a3e Exists: true
  Reading patch net.minecraft.world.level.block.TntBlock.binpatch
    Checksum: c550a806 Exists: true
  Reading patch net.minecraft.world.level.block.TrapDoorBlock$1.binpatch
    Checksum: c26338ae Exists: true
  Reading patch net.minecraft.world.level.block.TrapDoorBlock.binpatch
    Checksum: d190e0bb Exists: true
  Reading patch net.minecraft.world.level.block.TripWireBlock$1.binpatch
    Checksum: ae63286a Exists: true
  Reading patch net.minecraft.world.level.block.TripWireBlock.binpatch
    Checksum: 40e10bf0 Exists: true
  Reading patch net.minecraft.world.level.block.TripWireHookBlock$1.binpatch
    Checksum: da65c59e Exists: true
  Reading patch net.minecraft.world.level.block.TripWireHookBlock.binpatch
    Checksum: 642cba45 Exists: true
  Reading patch net.minecraft.world.level.block.TurtleEggBlock.binpatch
    Checksum: 113a8ab0 Exists: true
  Reading patch net.minecraft.world.level.block.VineBlock$1.binpatch
    Checksum: 22a322a8 Exists: true
  Reading patch net.minecraft.world.level.block.VineBlock.binpatch
    Checksum: b1142581 Exists: true
  Reading patch net.minecraft.world.level.block.WebBlock.binpatch
    Checksum: 6d19ad61 Exists: true
  Reading patch net.minecraft.world.level.block.entity.AbstractFurnaceBlockEntity$1.binpatch
    Checksum: e4a64273 Exists: true
  Reading patch net.minecraft.world.level.block.entity.AbstractFurnaceBlockEntity.binpatch
    Checksum: a3d1ba74 Exists: true
  Reading patch net.minecraft.world.level.block.entity.BaseContainerBlockEntity.binpatch
    Checksum: a489ce22 Exists: true
  Reading patch net.minecraft.world.level.block.entity.BeaconBlockEntity$1.binpatch
    Checksum: 416b3178 Exists: true
  Reading patch net.minecraft.world.level.block.entity.BeaconBlockEntity$BeaconBeamSection.binpatch
    Checksum: 65b2b6f4 Exists: true
  Reading patch net.minecraft.world.level.block.entity.BeaconBlockEntity.binpatch
    Checksum: e9785fb0 Exists: true
  Reading patch net.minecraft.world.level.block.entity.BlockEntity.binpatch
    Checksum: e36b9196 Exists: true
  Reading patch net.minecraft.world.level.block.entity.BrewingStandBlockEntity$1.binpatch
    Checksum: e77a1d63 Exists: true
  Reading patch net.minecraft.world.level.block.entity.BrewingStandBlockEntity.binpatch
    Checksum: 684492ba Exists: true
  Reading patch net.minecraft.world.level.block.entity.ChestBlockEntity$1.binpatch
    Checksum: 2b6fecb4 Exists: true
  Reading patch net.minecraft.world.level.block.entity.ChestBlockEntity.binpatch
    Checksum: 69025a8c Exists: true
  Reading patch net.minecraft.world.level.block.entity.ConduitBlockEntity.binpatch
    Checksum: 7974bf92 Exists: true
  Reading patch net.minecraft.world.level.block.entity.HopperBlockEntity.binpatch
    Checksum: 3ccc067a Exists: true
  Reading patch net.minecraft.world.level.block.entity.ShulkerBoxBlockEntity$1.binpatch
    Checksum: 91951556 Exists: true
  Reading patch net.minecraft.world.level.block.entity.ShulkerBoxBlockEntity$AnimationStatus.binpatch
    Checksum: 5b14bd5e Exists: true
  Reading patch net.minecraft.world.level.block.entity.ShulkerBoxBlockEntity.binpatch
    Checksum: 6eb362d7 Exists: true
  Reading patch net.minecraft.world.level.block.entity.SpawnerBlockEntity$1.binpatch
    Checksum: ce1d5ec1 Exists: true
  Reading patch net.minecraft.world.level.block.entity.SpawnerBlockEntity.binpatch
    Checksum: 747a5242 Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonBaseBlock$1.binpatch
    Checksum: 137f4cc7 Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonBaseBlock.binpatch
    Checksum: 4f972f31 Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonMovingBlockEntity$1.binpatch
    Checksum: e71d439a Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonMovingBlockEntity.binpatch
    Checksum: 5b8f7a0f Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonStructureResolver.binpatch
    Checksum: 99c0c89b Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$1.binpatch
    Checksum: a13dfa2 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$BlockStateBase$Cache.binpatch
    Checksum: e309a9b9 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$BlockStateBase.binpatch
    Checksum: 9b38364a Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$OffsetType.binpatch
    Checksum: da8687cc Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$Properties.binpatch
    Checksum: 3095e6d5 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$StateArgumentPredicate.binpatch
    Checksum: d1ee84c Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$StatePredicate.binpatch
    Checksum: 549c8d3a Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour.binpatch
    Checksum: 6c72bd57 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockState.binpatch
    Checksum: 9f46f174 Exists: true
  Reading patch net.minecraft.world.level.block.state.properties.WoodType.binpatch
    Checksum: 82abe2da Exists: true
  Reading patch net.minecraft.world.level.chunk.ChunkAccess$TicksToSave.binpatch
    Checksum: 256817ba Exists: true
  Reading patch net.minecraft.world.level.chunk.ChunkAccess.binpatch
    Checksum: 448200df Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$1.binpatch
    Checksum: b3ddd0e8 Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$BoundTickingBlockEntity.binpatch
    Checksum: da536da7 Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$EntityCreationType.binpatch
    Checksum: 7ce980c9 Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$PostLoadProcessor.binpatch
    Checksum: f9f37d75 Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$RebindableTickingBlockEntityWrapper.binpatch
    Checksum: fba31256 Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk.binpatch
    Checksum: 514f1462 Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer$Configuration.binpatch
    Checksum: f3e1b781 Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer$CountConsumer.binpatch
    Checksum: ca258b4b Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer$Data.binpatch
    Checksum: dd188163 Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer$Strategy$1.binpatch
    Checksum: e1630dae Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer$Strategy$2.binpatch
    Checksum: dc48fe57 Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer$Strategy.binpatch
    Checksum: 7b53d9f Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer.binpatch
    Checksum: 5aa37232 Exists: true
  Reading patch net.minecraft.world.level.chunk.ProtoChunk.binpatch
    Checksum: 5bdfc62b Exists: true
  Reading patch net.minecraft.world.level.chunk.storage.ChunkSerializer.binpatch
    Checksum: e108286 Exists: true
  Reading patch net.minecraft.world.level.chunk.storage.EntityStorage.binpatch
    Checksum: 622d40ce Exists: true
  Reading patch net.minecraft.world.level.dimension.end.EndDragonFight.binpatch
    Checksum: a44a324a Exists: true
  Reading patch net.minecraft.world.level.entity.PersistentEntitySectionManager$Callback.binpatch
    Checksum: ebbc5128 Exists: true
  Reading patch net.minecraft.world.level.entity.PersistentEntitySectionManager$ChunkLoadStatus.binpatch
    Checksum: b4b8bd3d Exists: true
  Reading patch net.minecraft.world.level.entity.PersistentEntitySectionManager.binpatch
    Checksum: a5f06b83 Exists: true
  Reading patch net.minecraft.world.level.entity.TransientEntitySectionManager$Callback.binpatch
    Checksum: ddc99ae0 Exists: true
  Reading patch net.minecraft.world.level.entity.TransientEntitySectionManager.binpatch
    Checksum: c531b887 Exists: true
  Reading patch net.minecraft.world.level.levelgen.Beardifier$1.binpatch
    Checksum: 184becc9 Exists: true
  Reading patch net.minecraft.world.level.levelgen.Beardifier$Rigid.binpatch
    Checksum: b56a7dca Exists: true
  Reading patch net.minecraft.world.level.levelgen.Beardifier.binpatch
    Checksum: 12c8377c Exists: true
  Reading patch net.minecraft.world.level.levelgen.DebugLevelSource.binpatch
    Checksum: a90c878c Exists: true
  Reading patch net.minecraft.world.level.levelgen.PatrolSpawner.binpatch
    Checksum: a3a4b9cd Exists: true
  Reading patch net.minecraft.world.level.levelgen.PhantomSpawner.binpatch
    Checksum: bf0bd083 Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.Feature.binpatch
    Checksum: 39b1a0b Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.MonsterRoomFeature.binpatch
    Checksum: d50b8eb1 Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.configurations.TreeConfiguration$TreeConfigurationBuilder.binpatch
    Checksum: 849e2a21 Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.configurations.TreeConfiguration.binpatch
    Checksum: 38be49f6 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.Structure$GenerationContext.binpatch
    Checksum: e82bc0e1 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.Structure$GenerationStub.binpatch
    Checksum: c49468db Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.Structure$StructureSettings.binpatch
    Checksum: 98b37858 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.Structure.binpatch
    Checksum: ea0e71ca Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.StructurePiece$1.binpatch
    Checksum: cb78cdfa Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.StructurePiece$BlockSelector.binpatch
    Checksum: 7aa816e1 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.StructurePiece.binpatch
    Checksum: 9941a9bb Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.StructureStart.binpatch
    Checksum: d9ab7a8b Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureProcessor.binpatch
    Checksum: 2f20f8a1 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$1.binpatch
    Checksum: 48424fe0 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$Palette.binpatch
    Checksum: fe28572a Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$SimplePalette.binpatch
    Checksum: 7552b296 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$StructureBlockInfo.binpatch
    Checksum: 605b882b Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$StructureEntityInfo.binpatch
    Checksum: 25ee21bb Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate.binpatch
    Checksum: fc6666 Exists: true
  Reading patch net.minecraft.world.level.lighting.BlockLightEngine.binpatch
    Checksum: 98e382f6 Exists: true
  Reading patch net.minecraft.world.level.lighting.DynamicGraphMinFixedPoint$1.binpatch
    Checksum: 96b3fae6 Exists: true
  Reading patch net.minecraft.world.level.lighting.DynamicGraphMinFixedPoint$2.binpatch
    Checksum: 16c2fa6e Exists: true
  Reading patch net.minecraft.world.level.lighting.DynamicGraphMinFixedPoint.binpatch
    Checksum: 39ed66a6 Exists: true
  Reading patch net.minecraft.world.level.lighting.LayerLightEngine.binpatch
    Checksum: 8a7ad77 Exists: true
  Reading patch net.minecraft.world.level.lighting.SkyLightEngine.binpatch
    Checksum: 6b52b536 Exists: true
  Reading patch net.minecraft.world.level.material.FlowingFluid$1.binpatch
    Checksum: ff610bf Exists: true
  Reading patch net.minecraft.world.level.material.FlowingFluid.binpatch
    Checksum: e4e5b8c2 Exists: true
  Reading patch net.minecraft.world.level.material.Fluid.binpatch
    Checksum: e1dc0466 Exists: true
  Reading patch net.minecraft.world.level.material.FluidState.binpatch
    Checksum: 8ad1b06 Exists: true
  Reading patch net.minecraft.world.level.material.LavaFluid$Flowing.binpatch
    Checksum: 8b8094e4 Exists: true
  Reading patch net.minecraft.world.level.material.LavaFluid$Source.binpatch
    Checksum: 6127bcc9 Exists: true
  Reading patch net.minecraft.world.level.material.LavaFluid.binpatch
    Checksum: 7140cb15 Exists: true
  Reading patch net.minecraft.world.level.pathfinder.AmphibiousNodeEvaluator.binpatch
    Checksum: 70152a06 Exists: true
  Reading patch net.minecraft.world.level.pathfinder.BlockPathTypes.binpatch
    Checksum: ad843cf5 Exists: true
  Reading patch net.minecraft.world.level.pathfinder.WalkNodeEvaluator.binpatch
    Checksum: a5dd4994 Exists: true
  Reading patch net.minecraft.world.level.portal.PortalForcer.binpatch
    Checksum: cd5085ff Exists: true
  Reading patch net.minecraft.world.level.portal.PortalShape.binpatch
    Checksum: 84cb20d4 Exists: true
  Reading patch net.minecraft.world.level.saveddata.maps.MapDecoration$Type.binpatch
    Checksum: faa320cf Exists: true
  Reading patch net.minecraft.world.level.saveddata.maps.MapDecoration.binpatch
    Checksum: c7f609b3 Exists: true
  Reading patch net.minecraft.world.level.storage.DimensionDataStorage.binpatch
    Checksum: 4d679332 Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelCandidates.binpatch
    Checksum: 39a9263f Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelDirectory.binpatch
    Checksum: b6c479c9 Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelStorageAccess$1.binpatch
    Checksum: ef203595 Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelStorageAccess$2.binpatch
    Checksum: 402d6b05 Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelStorageAccess.binpatch
    Checksum: a1da9f26 Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource.binpatch
    Checksum: d83d6307 Exists: true
  Reading patch net.minecraft.world.level.storage.LevelSummary$BackupStatus.binpatch
    Checksum: 9b0eefc Exists: true
  Reading patch net.minecraft.world.level.storage.LevelSummary.binpatch
    Checksum: e08c4303 Exists: true
  Reading patch net.minecraft.world.level.storage.PlayerDataStorage.binpatch
    Checksum: 9bfdb348 Exists: true
  Reading patch net.minecraft.world.level.storage.PrimaryLevelData.binpatch
    Checksum: f782adab Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext$Builder.binpatch
    Checksum: 6f4b3369 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext$DynamicDrop.binpatch
    Checksum: 49ddb59 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext$EntityTarget$Serializer.binpatch
    Checksum: ba6665a8 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext$EntityTarget.binpatch
    Checksum: 7c6d4a58 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext.binpatch
    Checksum: 185188df Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootPool$Builder.binpatch
    Checksum: d7800a8e Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootPool$Serializer.binpatch
    Checksum: feda0cb0 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootPool.binpatch
    Checksum: 74714ab1 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootTable$Builder.binpatch
    Checksum: 7a1596ca Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootTable$Serializer.binpatch
    Checksum: 8a808d8f Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootTable.binpatch
    Checksum: 388a9f77 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootTables.binpatch
    Checksum: 35cb83ba Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.LootingEnchantFunction$Builder.binpatch
    Checksum: f83c7cf2 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.LootingEnchantFunction$Serializer.binpatch
    Checksum: 8ee7efb6 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.LootingEnchantFunction.binpatch
    Checksum: f1591d55 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.SmeltItemFunction$Serializer.binpatch
    Checksum: 1230785b Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.SmeltItemFunction.binpatch
    Checksum: 3c96b49a Exists: true
  Reading patch net.minecraft.world.level.storage.loot.parameters.LootContextParamSets.binpatch
    Checksum: 269eeaf0 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.predicates.LootItemRandomChanceWithLootingCondition$Serializer.binpatch
    Checksum: c8cb141a Exists: true
  Reading patch net.minecraft.world.level.storage.loot.predicates.LootItemRandomChanceWithLootingCondition.binpatch
    Checksum: 1b26ebb Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider$1.binpatch
    Checksum: ace54e53 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider$2.binpatch
    Checksum: 2c4f0801 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider$Getter.binpatch
    Checksum: 3a94d9f8 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider$InlineSerializer.binpatch
    Checksum: 290252f5 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider$Serializer.binpatch
    Checksum: a8f146bd Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider.binpatch
    Checksum: 888f6b13 Exists: true
Processing: C:\Users\<USER>\.gradle\caches\forge_gradle\mcp_repo\net\minecraft\joined\1.19-20220607.102129\joined-1.19-20220607.102129-srg.jar
  Patching com/mojang/blaze3d/pipeline/RenderTarget 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$BlendState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$BooleanState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$ColorLogicState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$ColorMask 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$CullState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$DepthState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$DestFactor 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$LogicOp 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$PolygonOffsetState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$ScissorState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$SourceFactor 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$StencilFunc 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$StencilState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$TextureState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$Viewport 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager 1/1
  Patching com/mojang/blaze3d/platform/Window$WindowInitFailed 1/1
  Patching com/mojang/blaze3d/platform/Window 1/1
  Patching com/mojang/blaze3d/vertex/BufferBuilder$1 1/1
  Patching com/mojang/blaze3d/vertex/BufferBuilder$DrawState 1/1
  Patching com/mojang/blaze3d/vertex/BufferBuilder$RenderedBuffer 1/1
  Patching com/mojang/blaze3d/vertex/BufferBuilder$SortState 1/1
  Patching com/mojang/blaze3d/vertex/BufferBuilder 1/1
  Patching com/mojang/blaze3d/vertex/VertexConsumer 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormat$1 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormat$IndexType 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormat$Mode 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormat 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormatElement$Type 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormatElement$Usage$ClearState 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormatElement$Usage$SetupState 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormatElement$Usage 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormatElement 1/1
  Patching com/mojang/math/Matrix3f 1/1
  Patching com/mojang/math/Matrix4f 1/1
  Patching com/mojang/math/Transformation 1/1
  Patching com/mojang/math/Vector3f 1/1
  Patching com/mojang/math/Vector4f 1/1
  Patching com/mojang/realmsclient/gui/screens/RealmsGenericErrorScreen 1/1
  Patching net/minecraft/CrashReport 1/1
  Patching net/minecraft/CrashReportCategory$Entry 1/1
  Patching net/minecraft/CrashReportCategory 1/1
  Patching net/minecraft/SharedConstants$1 1/1
  Patching net/minecraft/SharedConstants 1/1
  Patching net/minecraft/Util$1 1/1
  Patching net/minecraft/Util$10 1/1
  Patching net/minecraft/Util$11 1/1
  Patching net/minecraft/Util$2 1/1
  Patching net/minecraft/Util$5 1/1
  Patching net/minecraft/Util$6 1/1
  Patching net/minecraft/Util$7 1/1
  Patching net/minecraft/Util$8 1/1
  Patching net/minecraft/Util$9 1/1
  Patching net/minecraft/Util$IdentityStrategy 1/1
  Patching net/minecraft/Util$OS$1 1/1
  Patching net/minecraft/Util$OS$2 1/1
  Patching net/minecraft/Util$OS 1/1
  Patching net/minecraft/Util 1/1
  Patching net/minecraft/advancements/Advancement$Builder 1/1
  Patching net/minecraft/advancements/Advancement 1/1
  Patching net/minecraft/advancements/AdvancementList$Listener 1/1
  Patching net/minecraft/advancements/AdvancementList 1/1
  Patching net/minecraft/advancements/AdvancementRewards$Builder 1/1
  Patching net/minecraft/advancements/AdvancementRewards 1/1
  Patching net/minecraft/advancements/critereon/ItemPredicate$Builder 1/1
  Patching net/minecraft/advancements/critereon/ItemPredicate 1/1
  Patching net/minecraft/client/Camera$NearPlane 1/1
  Patching net/minecraft/client/Camera 1/1
  Patching net/minecraft/client/ClientBrandRetriever 1/1
  Patching net/minecraft/client/ClientRecipeBook 1/1
  Patching net/minecraft/client/KeyMapping 1/1
  Patching net/minecraft/client/KeyboardHandler$1 1/1
  Patching net/minecraft/client/KeyboardHandler 1/1
  Patching net/minecraft/client/Minecraft$1 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus$1 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus$2 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus$3 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus$4 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus 1/1
  Patching net/minecraft/client/Minecraft 1/1
  Patching net/minecraft/client/MouseHandler 1/1
  Patching net/minecraft/client/Options$1 1/1
  Patching net/minecraft/client/Options$2 1/1
  Patching net/minecraft/client/Options$3 1/1
  Patching net/minecraft/client/Options$4 1/1
  Patching net/minecraft/client/Options$FieldAccess 1/1
  Patching net/minecraft/client/Options 1/1
  Patching net/minecraft/client/RecipeBookCategories$1 1/1
  Patching net/minecraft/client/RecipeBookCategories 1/1
  Patching net/minecraft/client/Screenshot 1/1
  Patching net/minecraft/client/ToggleKeyMapping 1/1
  Patching net/minecraft/client/User$Type 1/1
  Patching net/minecraft/client/User 1/1
  Patching net/minecraft/client/color/block/BlockColors 1/1
  Patching net/minecraft/client/color/item/ItemColors 1/1
  Patching net/minecraft/client/gui/Gui$HeartType 1/1
  Patching net/minecraft/client/gui/Gui 1/1
  Patching net/minecraft/client/gui/MapRenderer$MapInstance 1/1
  Patching net/minecraft/client/gui/MapRenderer 1/1
  Patching net/minecraft/client/gui/components/AbstractSelectionList$Entry 1/1
  Patching net/minecraft/client/gui/components/AbstractSelectionList$SelectionDirection 1/1
  Patching net/minecraft/client/gui/components/AbstractSelectionList$TrackedList 1/1
  Patching net/minecraft/client/gui/components/AbstractSelectionList 1/1
  Patching net/minecraft/client/gui/components/AbstractWidget 1/1
  Patching net/minecraft/client/gui/components/BossHealthOverlay$1 1/1
  Patching net/minecraft/client/gui/components/BossHealthOverlay 1/1
  Patching net/minecraft/client/gui/components/DebugScreenOverlay$1 1/1
  Patching net/minecraft/client/gui/components/DebugScreenOverlay$AllocationRateCalculator 1/1
  Patching net/minecraft/client/gui/components/DebugScreenOverlay 1/1
  Patching net/minecraft/client/gui/screens/DeathScreen 1/1
  Patching net/minecraft/client/gui/screens/LoadingOverlay$LogoTexture 1/1
  Patching net/minecraft/client/gui/screens/LoadingOverlay 1/1
  Patching net/minecraft/client/gui/screens/MenuScreens$ScreenConstructor 1/1
  Patching net/minecraft/client/gui/screens/MenuScreens 1/1
  Patching net/minecraft/client/gui/screens/OptionsScreen 1/1
  Patching net/minecraft/client/gui/screens/Screen$NarratableSearchResult 1/1
  Patching net/minecraft/client/gui/screens/Screen 1/1
  Patching net/minecraft/client/gui/screens/TitleScreen$1 1/1
  Patching net/minecraft/client/gui/screens/TitleScreen$WarningLabel 1/1
  Patching net/minecraft/client/gui/screens/TitleScreen 1/1
  Patching net/minecraft/client/gui/screens/advancements/AdvancementTab 1/1
  Patching net/minecraft/client/gui/screens/advancements/AdvancementTabType$1 1/1
  Patching net/minecraft/client/gui/screens/advancements/AdvancementTabType 1/1
  Patching net/minecraft/client/gui/screens/advancements/AdvancementsScreen 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsList$CategoryEntry$1 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsList$CategoryEntry 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsList$Entry 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsList$KeyEntry$1 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsList$KeyEntry$2 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsList$KeyEntry 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsList 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/AbstractContainerScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/CreativeModeInventoryScreen$CustomCreativeSlot 1/1
  Patching net/minecraft/client/gui/screens/inventory/CreativeModeInventoryScreen$ItemPickerMenu 1/1
  Patching net/minecraft/client/gui/screens/inventory/CreativeModeInventoryScreen$SlotWrapper 1/1
  Patching net/minecraft/client/gui/screens/inventory/CreativeModeInventoryScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/EffectRenderingInventoryScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/EnchantmentScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/MerchantScreen$TradeOfferButton 1/1
  Patching net/minecraft/client/gui/screens/inventory/MerchantScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/tooltip/ClientTooltipComponent 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/JoinMultiplayerScreen 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList$Entry 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList$LANHeader 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList$NetworkServerEntry 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList$OnlineServerEntry 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel$Entry 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel$EntryBase 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel$SelectedPackEntry 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel$UnselectedPackEntry 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionScreen$1 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionScreen$Watcher 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionScreen 1/1
  Patching net/minecraft/client/gui/screens/recipebook/RecipeBookComponent 1/1
  Patching net/minecraft/client/gui/screens/recipebook/RecipeBookPage 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$1 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$SelectedGameMode 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldOpenFlows 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldSelectionList$Entry 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldSelectionList$LoadingHeader 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldSelectionList$WorldListEntry 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldSelectionList 1/1
  Patching net/minecraft/client/main/Main$1 1/1
  Patching net/minecraft/client/main/Main$2 1/1
  Patching net/minecraft/client/main/Main$3 1/1
  Patching net/minecraft/client/main/Main 1/1
  Patching net/minecraft/client/model/geom/LayerDefinitions 1/1
  Patching net/minecraft/client/model/geom/ModelLayers 1/1
  Patching net/minecraft/client/multiplayer/ClientChunkCache$Storage 1/1
  Patching net/minecraft/client/multiplayer/ClientChunkCache 1/1
  Patching net/minecraft/client/multiplayer/ClientHandshakePacketListenerImpl 1/1
  Patching net/minecraft/client/multiplayer/ClientLevel$1 1/1
  Patching net/minecraft/client/multiplayer/ClientLevel$ClientLevelData 1/1
  Patching net/minecraft/client/multiplayer/ClientLevel$EntityCallbacks 1/1
  Patching net/minecraft/client/multiplayer/ClientLevel 1/1
  Patching net/minecraft/client/multiplayer/ClientPacketListener$1 1/1
  Patching net/minecraft/client/multiplayer/ClientPacketListener 1/1
  Patching net/minecraft/client/multiplayer/MultiPlayerGameMode 1/1
  Patching net/minecraft/client/multiplayer/PlayerInfo 1/1
  Patching net/minecraft/client/multiplayer/ProfileKeyPairManager 1/1
  Patching net/minecraft/client/multiplayer/ServerData$ChatPreview 1/1
  Patching net/minecraft/client/multiplayer/ServerData$ServerPackStatus 1/1
  Patching net/minecraft/client/multiplayer/ServerData 1/1
  Patching net/minecraft/client/multiplayer/ServerStatusPinger$1 1/1
  Patching net/minecraft/client/multiplayer/ServerStatusPinger$2$1 1/1
  Patching net/minecraft/client/multiplayer/ServerStatusPinger$2 1/1
  Patching net/minecraft/client/multiplayer/ServerStatusPinger 1/1
  Patching net/minecraft/client/particle/BreakingItemParticle$Provider 1/1
  Patching net/minecraft/client/particle/BreakingItemParticle$SlimeProvider 1/1
  Patching net/minecraft/client/particle/BreakingItemParticle$SnowballProvider 1/1
  Patching net/minecraft/client/particle/BreakingItemParticle 1/1
  Patching net/minecraft/client/particle/Particle 1/1
  Patching net/minecraft/client/particle/ParticleEngine$MutableSpriteSet 1/1
  Patching net/minecraft/client/particle/ParticleEngine$SpriteParticleRegistration 1/1
  Patching net/minecraft/client/particle/ParticleEngine 1/1
  Patching net/minecraft/client/particle/TerrainParticle$Provider 1/1
  Patching net/minecraft/client/particle/TerrainParticle 1/1
  Patching net/minecraft/client/player/AbstractClientPlayer 1/1
  Patching net/minecraft/client/player/LocalPlayer 1/1
  Patching net/minecraft/client/player/RemotePlayer 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects$EndEffects 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects$NetherEffects 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects$OverworldEffects 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects$SkyType 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects 1/1
  Patching net/minecraft/client/renderer/EffectInstance 1/1
  Patching net/minecraft/client/renderer/FogRenderer$BlindnessFogFunction 1/1
  Patching net/minecraft/client/renderer/FogRenderer$DarknessFogFunction 1/1
  Patching net/minecraft/client/renderer/FogRenderer$FogData 1/1
  Patching net/minecraft/client/renderer/FogRenderer$FogMode 1/1
  Patching net/minecraft/client/renderer/FogRenderer$MobEffectFogFunction 1/1
  Patching net/minecraft/client/renderer/FogRenderer 1/1
  Patching net/minecraft/client/renderer/GameRenderer 1/1
  Patching net/minecraft/client/renderer/ItemBlockRenderTypes 1/1
  Patching net/minecraft/client/renderer/ItemInHandRenderer$1 1/1
  Patching net/minecraft/client/renderer/ItemInHandRenderer$HandRenderSelection 1/1
  Patching net/minecraft/client/renderer/ItemInHandRenderer 1/1
  Patching net/minecraft/client/renderer/ItemModelShaper 1/1
  Patching net/minecraft/client/renderer/LevelRenderer$RenderChunkInfo 1/1
  Patching net/minecraft/client/renderer/LevelRenderer$RenderChunkStorage 1/1
  Patching net/minecraft/client/renderer/LevelRenderer$RenderInfoMap 1/1
  Patching net/minecraft/client/renderer/LevelRenderer$TransparencyShaderException 1/1
  Patching net/minecraft/client/renderer/LevelRenderer 1/1
  Patching net/minecraft/client/renderer/LightTexture 1/1
  Patching net/minecraft/client/renderer/PostChain 1/1
  Patching net/minecraft/client/renderer/RenderType$CompositeRenderType 1/1
  Patching net/minecraft/client/renderer/RenderType$CompositeState$CompositeStateBuilder 1/1
  Patching net/minecraft/client/renderer/RenderType$CompositeState 1/1
  Patching net/minecraft/client/renderer/RenderType$OutlineProperty 1/1
  Patching net/minecraft/client/renderer/RenderType 1/1
  Patching net/minecraft/client/renderer/ScreenEffectRenderer 1/1
  Patching net/minecraft/client/renderer/ShaderInstance$1 1/1
  Patching net/minecraft/client/renderer/ShaderInstance 1/1
  Patching net/minecraft/client/renderer/Sheets$1 1/1
  Patching net/minecraft/client/renderer/Sheets 1/1
  Patching net/minecraft/client/renderer/block/BlockModelShaper 1/1
  Patching net/minecraft/client/renderer/block/BlockRenderDispatcher$1 1/1
  Patching net/minecraft/client/renderer/block/BlockRenderDispatcher 1/1
  Patching net/minecraft/client/renderer/block/LiquidBlockRenderer$1 1/1
  Patching net/minecraft/client/renderer/block/LiquidBlockRenderer 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$1 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$AdjacencyInfo 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$AmbientOcclusionFace 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$AmbientVertexRemap 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$Cache$1 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$Cache$2 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$Cache 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$SizeInfo 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer 1/1
  Patching net/minecraft/client/renderer/block/model/BlockElementFace$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/BlockElementFace 1/1
  Patching net/minecraft/client/renderer/block/model/BlockModel$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/BlockModel$GuiLight 1/1
  Patching net/minecraft/client/renderer/block/model/BlockModel$LoopException 1/1
  Patching net/minecraft/client/renderer/block/model/BlockModel 1/1
  Patching net/minecraft/client/renderer/block/model/FaceBakery$1 1/1
  Patching net/minecraft/client/renderer/block/model/FaceBakery 1/1
  Patching net/minecraft/client/renderer/block/model/ItemModelGenerator$1 1/1
  Patching net/minecraft/client/renderer/block/model/ItemModelGenerator$Span 1/1
  Patching net/minecraft/client/renderer/block/model/ItemModelGenerator$SpanFacing 1/1
  Patching net/minecraft/client/renderer/block/model/ItemModelGenerator 1/1
  Patching net/minecraft/client/renderer/block/model/ItemOverrides$BakedOverride 1/1
  Patching net/minecraft/client/renderer/block/model/ItemOverrides$PropertyMatcher 1/1
  Patching net/minecraft/client/renderer/block/model/ItemOverrides 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransform$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransform 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransforms$1 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransforms$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransforms$TransformType 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransforms 1/1
  Patching net/minecraft/client/renderer/block/model/MultiVariant$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/MultiVariant 1/1
  Patching net/minecraft/client/renderer/blockentity/BlockEntityRenderers 1/1
  Patching net/minecraft/client/renderer/blockentity/ChestRenderer 1/1
  Patching net/minecraft/client/renderer/blockentity/PistonHeadRenderer 1/1
  Patching net/minecraft/client/renderer/blockentity/SkullBlockRenderer 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$ChunkTaskResult 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$CompiledChunk$1 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$CompiledChunk 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$RenderChunk$ChunkCompileTask 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$RenderChunk$RebuildTask$CompileResults 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$RenderChunk$RebuildTask 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$RenderChunk$ResortTransparencyTask 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$RenderChunk 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher 1/1
  Patching net/minecraft/client/renderer/chunk/RenderChunkRegion 1/1
  Patching net/minecraft/client/renderer/entity/BoatRenderer 1/1
  Patching net/minecraft/client/renderer/entity/EntityRenderDispatcher 1/1
  Patching net/minecraft/client/renderer/entity/EntityRenderer 1/1
  Patching net/minecraft/client/renderer/entity/FallingBlockRenderer 1/1
  Patching net/minecraft/client/renderer/entity/FishingHookRenderer 1/1
  Patching net/minecraft/client/renderer/entity/ItemEntityRenderer 1/1
  Patching net/minecraft/client/renderer/entity/ItemFrameRenderer 1/1
  Patching net/minecraft/client/renderer/entity/ItemRenderer 1/1
  Patching net/minecraft/client/renderer/entity/LivingEntityRenderer$1 1/1
  Patching net/minecraft/client/renderer/entity/LivingEntityRenderer 1/1
  Patching net/minecraft/client/renderer/entity/layers/ElytraLayer 1/1
  Patching net/minecraft/client/renderer/entity/layers/HumanoidArmorLayer$1 1/1
  Patching net/minecraft/client/renderer/entity/layers/HumanoidArmorLayer 1/1
  Patching net/minecraft/client/renderer/entity/player/PlayerRenderer 1/1
  Patching net/minecraft/client/renderer/item/ItemProperties$1 1/1
  Patching net/minecraft/client/renderer/item/ItemProperties 1/1
  Patching net/minecraft/client/renderer/texture/AbstractTexture 1/1
  Patching net/minecraft/client/renderer/texture/MipmapGenerator 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher$Holder 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher$Region 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher$SpriteLoader 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlas$Preparations 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlas 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlasSprite$AnimatedTexture 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlasSprite$FrameInfo 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlasSprite$Info 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlasSprite$InterpolationData 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlasSprite 1/1
  Patching net/minecraft/client/renderer/texture/TextureManager 1/1
  Patching net/minecraft/client/resources/language/ClientLanguage 1/1
  Patching net/minecraft/client/resources/language/I18n 1/1
  Patching net/minecraft/client/resources/language/LanguageInfo 1/1
  Patching net/minecraft/client/resources/model/BakedModel 1/1
  Patching net/minecraft/client/resources/model/ModelBakery$BlockStateDefinitionException 1/1
  Patching net/minecraft/client/resources/model/ModelBakery$ModelGroupKey 1/1
  Patching net/minecraft/client/resources/model/ModelBakery 1/1
  Patching net/minecraft/client/resources/model/ModelManager 1/1
  Patching net/minecraft/client/resources/model/ModelResourceLocation 1/1
  Patching net/minecraft/client/resources/model/MultiPartBakedModel$Builder 1/1
  Patching net/minecraft/client/resources/model/MultiPartBakedModel 1/1
  Patching net/minecraft/client/resources/model/SimpleBakedModel$Builder 1/1
  Patching net/minecraft/client/resources/model/SimpleBakedModel 1/1
  Patching net/minecraft/client/resources/model/WeightedBakedModel$Builder 1/1
  Patching net/minecraft/client/resources/model/WeightedBakedModel 1/1
  Patching net/minecraft/client/resources/sounds/SoundInstance$Attenuation 1/1
  Patching net/minecraft/client/resources/sounds/SoundInstance 1/1
  Patching net/minecraft/client/server/IntegratedServer 1/1
  Patching net/minecraft/client/server/LanServerDetection$LanServerDetector 1/1
  Patching net/minecraft/client/server/LanServerDetection$LanServerList 1/1
  Patching net/minecraft/client/server/LanServerDetection 1/1
  Patching net/minecraft/client/server/LanServerPinger 1/1
  Patching net/minecraft/client/sounds/SoundEngine$DeviceCheckState 1/1
  Patching net/minecraft/client/sounds/SoundEngine 1/1
  Patching net/minecraft/commands/CommandSourceStack 1/1
  Patching net/minecraft/commands/Commands$CommandSelection 1/1
  Patching net/minecraft/commands/Commands$ParseFunction 1/1
  Patching net/minecraft/commands/Commands 1/1
  Patching net/minecraft/commands/arguments/ObjectiveArgument 1/1
  Patching net/minecraft/commands/arguments/ResourceLocationArgument 1/1
  Patching net/minecraft/commands/arguments/TeamArgument 1/1
  Patching net/minecraft/commands/arguments/coordinates/BlockPosArgument 1/1
  Patching net/minecraft/commands/arguments/selector/EntitySelectorParser 1/1
  Patching net/minecraft/commands/synchronization/ArgumentTypeInfos 1/1
  Patching net/minecraft/core/Holder$Direct 1/1
  Patching net/minecraft/core/Holder$Kind 1/1
  Patching net/minecraft/core/Holder$Reference$Type 1/1
  Patching net/minecraft/core/Holder$Reference 1/1
  Patching net/minecraft/core/Holder 1/1
  Patching net/minecraft/core/MappedRegistry 1/1
  Patching net/minecraft/core/Registry$1 1/1
  Patching net/minecraft/core/Registry$RegistryBootstrap 1/1
  Patching net/minecraft/core/Registry 1/1
  Patching net/minecraft/core/RegistryAccess$1 1/1
  Patching net/minecraft/core/RegistryAccess$Frozen 1/1
  Patching net/minecraft/core/RegistryAccess$ImmutableRegistryAccess 1/1
  Patching net/minecraft/core/RegistryAccess$RegistryData 1/1
  Patching net/minecraft/core/RegistryAccess$RegistryEntry 1/1
  Patching net/minecraft/core/RegistryAccess$Writable 1/1
  Patching net/minecraft/core/RegistryAccess$WritableRegistryAccess 1/1
  Patching net/minecraft/core/RegistryAccess 1/1
  Patching net/minecraft/core/RegistryCodecs$1 1/1
  Patching net/minecraft/core/RegistryCodecs$RegistryEntry 1/1
  Patching net/minecraft/core/RegistryCodecs 1/1
  Patching net/minecraft/core/dispenser/BoatDispenseItemBehavior 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$1 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$10 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$11 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$12 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$13 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$14 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$15 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$16 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$17 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$18 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$19 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$2 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$20 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$21 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$22 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$23 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$24 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$25 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$26 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$27 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$3 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$4 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$5 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$6 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$7$1 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$7 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$8$1 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$8 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$9 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior 1/1
  Patching net/minecraft/core/particles/BlockParticleOption$1 1/1
  Patching net/minecraft/core/particles/BlockParticleOption 1/1
  Patching net/minecraft/core/particles/ItemParticleOption$1 1/1
  Patching net/minecraft/core/particles/ItemParticleOption 1/1
  Patching net/minecraft/data/BuiltinRegistries$RegistryBootstrap 1/1
  Patching net/minecraft/data/BuiltinRegistries 1/1
  Patching net/minecraft/data/DataGenerator$PathProvider 1/1
  Patching net/minecraft/data/DataGenerator$Target 1/1
  Patching net/minecraft/data/DataGenerator 1/1
  Patching net/minecraft/data/HashCache$CacheUpdater 1/1
  Patching net/minecraft/data/HashCache$ProviderCache 1/1
  Patching net/minecraft/data/HashCache 1/1
  Patching net/minecraft/data/Main 1/1
  Patching net/minecraft/data/advancements/AdvancementProvider 1/1
  Patching net/minecraft/data/info/WorldgenRegistryDumpReport 1/1
  Patching net/minecraft/data/loot/BlockLoot 1/1
  Patching net/minecraft/data/loot/EntityLoot 1/1
  Patching net/minecraft/data/loot/LootTableProvider 1/1
  Patching net/minecraft/data/recipes/RecipeProvider 1/1
  Patching net/minecraft/data/tags/BannerPatternTagsProvider 1/1
  Patching net/minecraft/data/tags/BiomeTagsProvider 1/1
  Patching net/minecraft/data/tags/BlockTagsProvider 1/1
  Patching net/minecraft/data/tags/CatVariantTagsProvider 1/1
  Patching net/minecraft/data/tags/EntityTypeTagsProvider 1/1
  Patching net/minecraft/data/tags/FlatLevelGeneratorPresetTagsProvider 1/1
  Patching net/minecraft/data/tags/FluidTagsProvider 1/1
  Patching net/minecraft/data/tags/GameEventTagsProvider 1/1
  Patching net/minecraft/data/tags/InstrumentTagsProvider 1/1
  Patching net/minecraft/data/tags/ItemTagsProvider 1/1
  Patching net/minecraft/data/tags/PaintingVariantTagsProvider 1/1
  Patching net/minecraft/data/tags/PoiTypeTagsProvider 1/1
  Patching net/minecraft/data/tags/StructureTagsProvider 1/1
  Patching net/minecraft/data/tags/TagsProvider$TagAppender 1/1
  Patching net/minecraft/data/tags/TagsProvider 1/1
  Patching net/minecraft/data/tags/WorldPresetTagsProvider 1/1
  Patching net/minecraft/data/worldgen/biome/OverworldBiomes 1/1
  Patching net/minecraft/gametest/framework/GameTest 1/1
  Patching net/minecraft/gametest/framework/GameTestRegistry 1/1
  Patching net/minecraft/gametest/framework/GameTestServer$1 1/1
  Patching net/minecraft/gametest/framework/GameTestServer 1/1
  Patching net/minecraft/gametest/framework/StructureUtils$1 1/1
  Patching net/minecraft/gametest/framework/StructureUtils 1/1
  Patching net/minecraft/locale/Language$1 1/1
  Patching net/minecraft/locale/Language 1/1
  Patching net/minecraft/nbt/CompoundTag$1 1/1
  Patching net/minecraft/nbt/CompoundTag$2 1/1
  Patching net/minecraft/nbt/CompoundTag 1/1
  Patching net/minecraft/nbt/NbtAccounter$1 1/1
  Patching net/minecraft/nbt/NbtAccounter 1/1
  Patching net/minecraft/nbt/NbtIo$1 1/1
  Patching net/minecraft/nbt/NbtIo 1/1
  Patching net/minecraft/nbt/StringTag$1 1/1
  Patching net/minecraft/nbt/StringTag 1/1
  Patching net/minecraft/network/CompressionEncoder 1/1
  Patching net/minecraft/network/Connection$1 1/1
  Patching net/minecraft/network/Connection$2 1/1
  Patching net/minecraft/network/Connection$PacketHolder 1/1
  Patching net/minecraft/network/Connection 1/1
  Patching net/minecraft/network/FriendlyByteBuf$Reader 1/1
  Patching net/minecraft/network/FriendlyByteBuf$Writer 1/1
  Patching net/minecraft/network/FriendlyByteBuf 1/1
  Patching net/minecraft/network/chat/ChatDecorator 1/1
  Patching net/minecraft/network/chat/PlayerChatMessage 1/1
  Patching net/minecraft/network/chat/contents/TranslatableContents 1/1
  Patching net/minecraft/network/protocol/game/ClientboundCustomPayloadPacket 1/1
  Patching net/minecraft/network/protocol/game/ServerboundContainerClickPacket 1/1
  Patching net/minecraft/network/protocol/game/ServerboundCustomPayloadPacket 1/1
  Patching net/minecraft/network/protocol/game/ServerboundSetCreativeModeSlotPacket 1/1
  Patching net/minecraft/network/protocol/handshake/ClientIntentionPacket 1/1
  Patching net/minecraft/network/protocol/login/ClientboundCustomQueryPacket 1/1
  Patching net/minecraft/network/protocol/login/ServerboundCustomQueryPacket 1/1
  Patching net/minecraft/network/protocol/status/ClientboundStatusResponsePacket 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus$Players$Serializer 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus$Players 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus$Serializer 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus$Version$Serializer 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus$Version 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$1 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$2 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$3 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$4 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$5 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$6 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$7 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers 1/1
  Patching net/minecraft/network/syncher/SynchedEntityData$DataItem 1/1
  Patching net/minecraft/network/syncher/SynchedEntityData 1/1
  Patching net/minecraft/recipebook/PlaceRecipe 1/1
  Patching net/minecraft/resources/RegistryResourceAccess$1 1/1
  Patching net/minecraft/resources/RegistryResourceAccess$EntryThunk 1/1
  Patching net/minecraft/resources/RegistryResourceAccess$InMemoryStorage$Entry 1/1
  Patching net/minecraft/resources/RegistryResourceAccess$InMemoryStorage 1/1
  Patching net/minecraft/resources/RegistryResourceAccess$ParsedEntry 1/1
  Patching net/minecraft/resources/RegistryResourceAccess 1/1
  Patching net/minecraft/resources/ResourceKey 1/1
  Patching net/minecraft/resources/ResourceLocation$Serializer 1/1
  Patching net/minecraft/resources/ResourceLocation 1/1
  Patching net/minecraft/server/Bootstrap$1 1/1
  Patching net/minecraft/server/Bootstrap 1/1
  Patching net/minecraft/server/Eula 1/1
  Patching net/minecraft/server/Main$1 1/1
  Patching net/minecraft/server/Main 1/1
  Patching net/minecraft/server/MinecraftServer$1 1/1
  Patching net/minecraft/server/MinecraftServer$ReloadableResources 1/1
  Patching net/minecraft/server/MinecraftServer$ServerResourcePackInfo 1/1
  Patching net/minecraft/server/MinecraftServer$TimeProfiler$1 1/1
  Patching net/minecraft/server/MinecraftServer$TimeProfiler 1/1
  Patching net/minecraft/server/MinecraftServer 1/1
  Patching net/minecraft/server/PlayerAdvancements$1 1/1
  Patching net/minecraft/server/PlayerAdvancements 1/1
  Patching net/minecraft/server/ReloadableServerResources 1/1
  Patching net/minecraft/server/ServerAdvancementManager 1/1
  Patching net/minecraft/server/commands/SpreadPlayersCommand$Position 1/1
  Patching net/minecraft/server/commands/SpreadPlayersCommand 1/1
  Patching net/minecraft/server/commands/TeleportCommand$LookAt 1/1
  Patching net/minecraft/server/commands/TeleportCommand 1/1
  Patching net/minecraft/server/dedicated/DedicatedServer$1 1/1
  Patching net/minecraft/server/dedicated/DedicatedServer 1/1
  Patching net/minecraft/server/dedicated/ServerWatchdog$1 1/1
  Patching net/minecraft/server/dedicated/ServerWatchdog 1/1
  Patching net/minecraft/server/dedicated/Settings$MutableValue 1/1
  Patching net/minecraft/server/dedicated/Settings 1/1
  Patching net/minecraft/server/gui/MinecraftServerGui$1 1/1
  Patching net/minecraft/server/gui/MinecraftServerGui$2 1/1
  Patching net/minecraft/server/gui/MinecraftServerGui 1/1
  Patching net/minecraft/server/level/ChunkHolder$1 1/1
  Patching net/minecraft/server/level/ChunkHolder$ChunkLoadingFailure$1 1/1
  Patching net/minecraft/server/level/ChunkHolder$ChunkLoadingFailure 1/1
  Patching net/minecraft/server/level/ChunkHolder$ChunkSaveDebug 1/1
  Patching net/minecraft/server/level/ChunkHolder$FullChunkStatus 1/1
  Patching net/minecraft/server/level/ChunkHolder$LevelChangeListener 1/1
  Patching net/minecraft/server/level/ChunkHolder$PlayerProvider 1/1
  Patching net/minecraft/server/level/ChunkHolder 1/1
  Patching net/minecraft/server/level/ChunkMap$1 1/1
  Patching net/minecraft/server/level/ChunkMap$2 1/1
  Patching net/minecraft/server/level/ChunkMap$DistanceManager 1/1
  Patching net/minecraft/server/level/ChunkMap$TrackedEntity 1/1
  Patching net/minecraft/server/level/ChunkMap 1/1
  Patching net/minecraft/server/level/DistanceManager$ChunkTicketTracker 1/1
  Patching net/minecraft/server/level/DistanceManager$FixedPlayerDistanceChunkTracker 1/1
  Patching net/minecraft/server/level/DistanceManager$PlayerTicketTracker 1/1
  Patching net/minecraft/server/level/DistanceManager 1/1
  Patching net/minecraft/server/level/ServerChunkCache$ChunkAndHolder 1/1
  Patching net/minecraft/server/level/ServerChunkCache$MainThreadExecutor 1/1
  Patching net/minecraft/server/level/ServerChunkCache 1/1
  Patching net/minecraft/server/level/ServerEntity 1/1
  Patching net/minecraft/server/level/ServerLevel$EntityCallbacks 1/1
  Patching net/minecraft/server/level/ServerLevel 1/1
  Patching net/minecraft/server/level/ServerPlayer$1 1/1
  Patching net/minecraft/server/level/ServerPlayer$2 1/1
  Patching net/minecraft/server/level/ServerPlayer$3 1/1
  Patching net/minecraft/server/level/ServerPlayer 1/1
  Patching net/minecraft/server/level/ServerPlayerGameMode 1/1
  Patching net/minecraft/server/level/Ticket 1/1
  Patching net/minecraft/server/network/MemoryServerHandshakePacketListenerImpl 1/1
  Patching net/minecraft/server/network/ServerConnectionListener$1 1/1
  Patching net/minecraft/server/network/ServerConnectionListener$2 1/1
  Patching net/minecraft/server/network/ServerConnectionListener$LatencySimulator$DelayedMessage 1/1
  Patching net/minecraft/server/network/ServerConnectionListener$LatencySimulator 1/1
  Patching net/minecraft/server/network/ServerConnectionListener 1/1
  Patching net/minecraft/server/network/ServerGamePacketListenerImpl$1 1/1
  Patching net/minecraft/server/network/ServerGamePacketListenerImpl$2 1/1
  Patching net/minecraft/server/network/ServerGamePacketListenerImpl$EntityInteraction 1/1
  Patching net/minecraft/server/network/ServerGamePacketListenerImpl 1/1
  Patching net/minecraft/server/network/ServerHandshakePacketListenerImpl$1 1/1
  Patching net/minecraft/server/network/ServerHandshakePacketListenerImpl 1/1
  Patching net/minecraft/server/network/ServerLoginPacketListenerImpl$1 1/1
  Patching net/minecraft/server/network/ServerLoginPacketListenerImpl$PublicKeyParseException 1/1
  Patching net/minecraft/server/network/ServerLoginPacketListenerImpl$State 1/1
  Patching net/minecraft/server/network/ServerLoginPacketListenerImpl 1/1
  Patching net/minecraft/server/packs/AbstractPackResources 1/1
  Patching net/minecraft/server/packs/PackResources 1/1
  Patching net/minecraft/server/packs/VanillaPackResources 1/1
  Patching net/minecraft/server/packs/metadata/pack/PackMetadataSection 1/1
  Patching net/minecraft/server/packs/metadata/pack/PackMetadataSectionSerializer 1/1
  Patching net/minecraft/server/packs/repository/Pack$PackConstructor 1/1
  Patching net/minecraft/server/packs/repository/Pack$Position 1/1
  Patching net/minecraft/server/packs/repository/Pack 1/1
  Patching net/minecraft/server/packs/repository/PackCompatibility 1/1
  Patching net/minecraft/server/packs/repository/PackRepository 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$EntryStack 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$LeakedResourceWarningInputStream 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$PackEntry 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$SinglePackResourceThunkSupplier 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager 1/1
  Patching net/minecraft/server/packs/resources/MultiPackResourceManager 1/1
  Patching net/minecraft/server/packs/resources/ReloadableResourceManager 1/1
  Patching net/minecraft/server/packs/resources/SimpleJsonResourceReloadListener 1/1
  Patching net/minecraft/server/players/PlayerList$1 1/1
  Patching net/minecraft/server/players/PlayerList 1/1
  Patching net/minecraft/server/rcon/RconConsoleSource 1/1
  Patching net/minecraft/server/rcon/thread/RconClient 1/1
  Patching net/minecraft/stats/RecipeBookSettings$TypeSettings 1/1
  Patching net/minecraft/stats/RecipeBookSettings 1/1
  Patching net/minecraft/tags/BlockTags 1/1
  Patching net/minecraft/tags/FluidTags 1/1
  Patching net/minecraft/tags/ItemTags 1/1
  Patching net/minecraft/tags/TagBuilder 1/1
  Patching net/minecraft/tags/TagEntry$Lookup 1/1
  Patching net/minecraft/tags/TagEntry 1/1
  Patching net/minecraft/tags/TagLoader$1 1/1
  Patching net/minecraft/tags/TagLoader$EntryWithSource 1/1
  Patching net/minecraft/tags/TagLoader 1/1
  Patching net/minecraft/tags/TagManager$LoadResult 1/1
  Patching net/minecraft/tags/TagManager 1/1
  Patching net/minecraft/util/datafix/fixes/StructuresBecomeConfiguredFix$Conversion 1/1
  Patching net/minecraft/util/datafix/fixes/StructuresBecomeConfiguredFix 1/1
  Patching net/minecraft/world/effect/MobEffect 1/1
  Patching net/minecraft/world/effect/MobEffectInstance$FactorData 1/1
  Patching net/minecraft/world/effect/MobEffectInstance 1/1
  Patching net/minecraft/world/entity/Entity$1 1/1
  Patching net/minecraft/world/entity/Entity$MoveFunction 1/1
  Patching net/minecraft/world/entity/Entity$MovementEmission 1/1
  Patching net/minecraft/world/entity/Entity$RemovalReason 1/1
  Patching net/minecraft/world/entity/Entity 1/1
  Patching net/minecraft/world/entity/EntityType$1 1/1
  Patching net/minecraft/world/entity/EntityType$Builder 1/1
  Patching net/minecraft/world/entity/EntityType$EntityFactory 1/1
  Patching net/minecraft/world/entity/EntityType 1/1
  Patching net/minecraft/world/entity/ExperienceOrb 1/1
  Patching net/minecraft/world/entity/FlyingMob 1/1
  Patching net/minecraft/world/entity/LightningBolt 1/1
  Patching net/minecraft/world/entity/LivingEntity$1 1/1
  Patching net/minecraft/world/entity/LivingEntity$Fallsounds 1/1
  Patching net/minecraft/world/entity/LivingEntity 1/1
  Patching net/minecraft/world/entity/Mob$1 1/1
  Patching net/minecraft/world/entity/Mob 1/1
  Patching net/minecraft/world/entity/MobCategory 1/1
  Patching net/minecraft/world/entity/Shearable 1/1
  Patching net/minecraft/world/entity/SpawnPlacements$Data 1/1
  Patching net/minecraft/world/entity/SpawnPlacements$SpawnPredicate 1/1
  Patching net/minecraft/world/entity/SpawnPlacements$Type 1/1
  Patching net/minecraft/world/entity/SpawnPlacements 1/1
  Patching net/minecraft/world/entity/TamableAnimal 1/1
  Patching net/minecraft/world/entity/ai/attributes/AttributeSupplier$Builder 1/1
  Patching net/minecraft/world/entity/ai/attributes/AttributeSupplier 1/1
  Patching net/minecraft/world/entity/ai/attributes/DefaultAttributes 1/1
  Patching net/minecraft/world/entity/ai/behavior/CrossbowAttack$CrossbowState 1/1
  Patching net/minecraft/world/entity/ai/behavior/CrossbowAttack 1/1
  Patching net/minecraft/world/entity/ai/behavior/HarvestFarmland 1/1
  Patching net/minecraft/world/entity/ai/behavior/Swim 1/1
  Patching net/minecraft/world/entity/ai/control/MoveControl$Operation 1/1
  Patching net/minecraft/world/entity/ai/control/MoveControl 1/1
  Patching net/minecraft/world/entity/ai/goal/BreakDoorGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/EatBlockGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/FloatGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/MeleeAttackGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/RangedBowAttackGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/RangedCrossbowAttackGoal$CrossbowState 1/1
  Patching net/minecraft/world/entity/ai/goal/RangedCrossbowAttackGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/RemoveBlockGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/RunAroundLikeCrazyGoal 1/1
  Patching net/minecraft/world/entity/ai/navigation/PathNavigation 1/1
  Patching net/minecraft/world/entity/ai/navigation/WallClimberNavigation 1/1
  Patching net/minecraft/world/entity/ai/village/VillageSiege$State 1/1
  Patching net/minecraft/world/entity/ai/village/VillageSiege 1/1
  Patching net/minecraft/world/entity/ai/village/poi/PoiTypes 1/1
  Patching net/minecraft/world/entity/animal/Animal 1/1
  Patching net/minecraft/world/entity/animal/Bee$1 1/1
  Patching net/minecraft/world/entity/animal/Bee$BaseBeeGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeAttackGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeBecomeAngryTargetGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeEnterHiveGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeGoToHiveGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeGoToKnownFlowerGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeGrowCropGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeHurtByOtherGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeLocateHiveGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeLookControl 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeePollinateGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeWanderGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee 1/1
  Patching net/minecraft/world/entity/animal/Cat$CatAvoidEntityGoal 1/1
  Patching net/minecraft/world/entity/animal/Cat$CatRelaxOnOwnerGoal 1/1
  Patching net/minecraft/world/entity/animal/Cat$CatTemptGoal 1/1
  Patching net/minecraft/world/entity/animal/Cat 1/1
  Patching net/minecraft/world/entity/animal/Fox$DefendTrustedTargetGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FaceplantGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxAlertableEntitiesSelector 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxBehaviorGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxBreedGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxEatBerriesGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxFloatGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxFollowParentGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxGroupData 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxLookAtPlayerGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxLookControl 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxMeleeAttackGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxMoveControl 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxPanicGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxPounceGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxSearchForItemsGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxStrollThroughVillageGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$PerchAndSearchGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$SeekShelterGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$SleepGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$StalkPreyGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$Type 1/1
  Patching net/minecraft/world/entity/animal/Fox 1/1
  Patching net/minecraft/world/entity/animal/IronGolem$Crackiness 1/1
  Patching net/minecraft/world/entity/animal/IronGolem 1/1
  Patching net/minecraft/world/entity/animal/MushroomCow$MushroomType 1/1
  Patching net/minecraft/world/entity/animal/MushroomCow 1/1
  Patching net/minecraft/world/entity/animal/Ocelot$OcelotAvoidEntityGoal 1/1
  Patching net/minecraft/world/entity/animal/Ocelot$OcelotTemptGoal 1/1
  Patching net/minecraft/world/entity/animal/Ocelot 1/1
  Patching net/minecraft/world/entity/animal/Parrot$1 1/1
  Patching net/minecraft/world/entity/animal/Parrot$ParrotWanderGoal 1/1
  Patching net/minecraft/world/entity/animal/Parrot 1/1
  Patching net/minecraft/world/entity/animal/Pig 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$EvilRabbitAttackGoal 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitAvoidEntityGoal 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitGroupData 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitJumpControl 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitMoveControl 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitPanicGoal 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RaidGardenGoal 1/1
  Patching net/minecraft/world/entity/animal/Rabbit 1/1
  Patching net/minecraft/world/entity/animal/Sheep$1 1/1
  Patching net/minecraft/world/entity/animal/Sheep$2 1/1
  Patching net/minecraft/world/entity/animal/Sheep 1/1
  Patching net/minecraft/world/entity/animal/SnowGolem 1/1
  Patching net/minecraft/world/entity/animal/Wolf$WolfAvoidEntityGoal 1/1
  Patching net/minecraft/world/entity/animal/Wolf$WolfPanicGoal 1/1
  Patching net/minecraft/world/entity/animal/Wolf 1/1
  Patching net/minecraft/world/entity/animal/horse/AbstractHorse$1 1/1
  Patching net/minecraft/world/entity/animal/horse/AbstractHorse 1/1
  Patching net/minecraft/world/entity/animal/horse/Horse$HorseGroupData 1/1
  Patching net/minecraft/world/entity/animal/horse/Horse 1/1
  Patching net/minecraft/world/entity/animal/horse/SkeletonTrapGoal 1/1
  Patching net/minecraft/world/entity/boss/EnderDragonPart 1/1
  Patching net/minecraft/world/entity/boss/enderdragon/EnderDragon 1/1
  Patching net/minecraft/world/entity/boss/wither/WitherBoss$WitherDoNothingGoal 1/1
  Patching net/minecraft/world/entity/boss/wither/WitherBoss 1/1
  Patching net/minecraft/world/entity/decoration/ArmorStand$1 1/1
  Patching net/minecraft/world/entity/decoration/ArmorStand 1/1
  Patching net/minecraft/world/entity/decoration/HangingEntity$1 1/1
  Patching net/minecraft/world/entity/decoration/HangingEntity 1/1
  Patching net/minecraft/world/entity/item/FallingBlockEntity 1/1
  Patching net/minecraft/world/entity/item/ItemEntity 1/1
  Patching net/minecraft/world/entity/monster/AbstractSkeleton$1 1/1
  Patching net/minecraft/world/entity/monster/AbstractSkeleton 1/1
  Patching net/minecraft/world/entity/monster/Creeper 1/1
  Patching net/minecraft/world/entity/monster/CrossbowAttackMob 1/1
  Patching net/minecraft/world/entity/monster/EnderMan$EndermanFreezeWhenLookedAt 1/1
  Patching net/minecraft/world/entity/monster/EnderMan$EndermanLeaveBlockGoal 1/1
  Patching net/minecraft/world/entity/monster/EnderMan$EndermanLookForPlayerGoal 1/1
  Patching net/minecraft/world/entity/monster/EnderMan$EndermanTakeBlockGoal 1/1
  Patching net/minecraft/world/entity/monster/EnderMan 1/1
  Patching net/minecraft/world/entity/monster/Evoker$EvokerAttackSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Evoker$EvokerCastingSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Evoker$EvokerSummonSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Evoker$EvokerWololoSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Evoker 1/1
  Patching net/minecraft/world/entity/monster/Illusioner$IllusionerBlindnessSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Illusioner$IllusionerMirrorSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Illusioner 1/1
  Patching net/minecraft/world/entity/monster/MagmaCube 1/1
  Patching net/minecraft/world/entity/monster/Monster 1/1
  Patching net/minecraft/world/entity/monster/Pillager 1/1
  Patching net/minecraft/world/entity/monster/Ravager$RavagerMeleeAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Ravager$RavagerNavigation 1/1
  Patching net/minecraft/world/entity/monster/Ravager$RavagerNodeEvaluator 1/1
  Patching net/minecraft/world/entity/monster/Ravager 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerBodyRotationControl 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerDefenseAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerLookControl 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerNearestAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerPeekGoal 1/1
  Patching net/minecraft/world/entity/monster/Shulker 1/1
  Patching net/minecraft/world/entity/monster/Silverfish$SilverfishMergeWithStoneGoal 1/1
  Patching net/minecraft/world/entity/monster/Silverfish$SilverfishWakeUpFriendsGoal 1/1
  Patching net/minecraft/world/entity/monster/Silverfish 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeFloatGoal 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeKeepOnJumpingGoal 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeMoveControl 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeRandomDirectionGoal 1/1
  Patching net/minecraft/world/entity/monster/Slime 1/1
  Patching net/minecraft/world/entity/monster/Spider$SpiderAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Spider$SpiderEffectsGroupData 1/1
  Patching net/minecraft/world/entity/monster/Spider$SpiderTargetGoal 1/1
  Patching net/minecraft/world/entity/monster/Spider 1/1
  Patching net/minecraft/world/entity/monster/Zombie$ZombieAttackTurtleEggGoal 1/1
  Patching net/minecraft/world/entity/monster/Zombie$ZombieGroupData 1/1
  Patching net/minecraft/world/entity/monster/Zombie 1/1
  Patching net/minecraft/world/entity/monster/ZombieVillager 1/1
  Patching net/minecraft/world/entity/monster/hoglin/Hoglin 1/1
  Patching net/minecraft/world/entity/monster/piglin/AbstractPiglin 1/1
  Patching net/minecraft/world/entity/monster/piglin/Piglin 1/1
  Patching net/minecraft/world/entity/monster/piglin/PiglinAi 1/1
  Patching net/minecraft/world/entity/monster/piglin/StopHoldingItemIfNoLongerAdmiring 1/1
  Patching net/minecraft/world/entity/npc/AbstractVillager 1/1
  Patching net/minecraft/world/entity/npc/CatSpawner 1/1
  Patching net/minecraft/world/entity/npc/Villager 1/1
  Patching net/minecraft/world/entity/player/Inventory 1/1
  Patching net/minecraft/world/entity/player/Player$1 1/1
  Patching net/minecraft/world/entity/player/Player$BedSleepingProblem 1/1
  Patching net/minecraft/world/entity/player/Player 1/1
  Patching net/minecraft/world/entity/projectile/AbstractArrow$1 1/1
  Patching net/minecraft/world/entity/projectile/AbstractArrow$Pickup 1/1
  Patching net/minecraft/world/entity/projectile/AbstractArrow 1/1
  Patching net/minecraft/world/entity/projectile/AbstractHurtingProjectile 1/1
  Patching net/minecraft/world/entity/projectile/FireworkRocketEntity 1/1
  Patching net/minecraft/world/entity/projectile/FishingHook$1 1/1
  Patching net/minecraft/world/entity/projectile/FishingHook$FishHookState 1/1
  Patching net/minecraft/world/entity/projectile/FishingHook$OpenWaterType 1/1
  Patching net/minecraft/world/entity/projectile/FishingHook 1/1
  Patching net/minecraft/world/entity/projectile/LargeFireball 1/1
  Patching net/minecraft/world/entity/projectile/LlamaSpit 1/1
  Patching net/minecraft/world/entity/projectile/Projectile 1/1
  Patching net/minecraft/world/entity/projectile/ProjectileUtil 1/1
  Patching net/minecraft/world/entity/projectile/ShulkerBullet 1/1
  Patching net/minecraft/world/entity/projectile/SmallFireball 1/1
  Patching net/minecraft/world/entity/projectile/ThrowableProjectile 1/1
  Patching net/minecraft/world/entity/projectile/ThrownEnderpearl 1/1
  Patching net/minecraft/world/entity/projectile/WitherSkull 1/1
  Patching net/minecraft/world/entity/raid/Raid$1 1/1
  Patching net/minecraft/world/entity/raid/Raid$RaidStatus 1/1
  Patching net/minecraft/world/entity/raid/Raid$RaiderType 1/1
  Patching net/minecraft/world/entity/raid/Raid 1/1
  Patching net/minecraft/world/entity/vehicle/AbstractMinecart$1 1/1
  Patching net/minecraft/world/entity/vehicle/AbstractMinecart$Type 1/1
  Patching net/minecraft/world/entity/vehicle/AbstractMinecart 1/1
  Patching net/minecraft/world/entity/vehicle/AbstractMinecartContainer 1/1
  Patching net/minecraft/world/entity/vehicle/Boat$1 1/1
  Patching net/minecraft/world/entity/vehicle/Boat$Status 1/1
  Patching net/minecraft/world/entity/vehicle/Boat$Type 1/1
  Patching net/minecraft/world/entity/vehicle/Boat 1/1
  Patching net/minecraft/world/entity/vehicle/ChestBoat$1 1/1
  Patching net/minecraft/world/entity/vehicle/ChestBoat 1/1
  Patching net/minecraft/world/entity/vehicle/ContainerEntity$1 1/1
  Patching net/minecraft/world/entity/vehicle/ContainerEntity 1/1
  Patching net/minecraft/world/entity/vehicle/Minecart 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartCommandBlock$MinecartCommandBase 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartCommandBlock 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartFurnace 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartSpawner$1 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartSpawner 1/1
  Patching net/minecraft/world/food/FoodData 1/1
  Patching net/minecraft/world/food/FoodProperties$Builder 1/1
  Patching net/minecraft/world/food/FoodProperties 1/1
  Patching net/minecraft/world/inventory/AbstractContainerMenu$1 1/1
  Patching net/minecraft/world/inventory/AbstractContainerMenu 1/1
  Patching net/minecraft/world/inventory/AbstractFurnaceMenu 1/1
  Patching net/minecraft/world/inventory/AnvilMenu$1 1/1
  Patching net/minecraft/world/inventory/AnvilMenu 1/1
  Patching net/minecraft/world/inventory/BeaconMenu$1 1/1
  Patching net/minecraft/world/inventory/BeaconMenu$PaymentSlot 1/1
  Patching net/minecraft/world/inventory/BeaconMenu 1/1
  Patching net/minecraft/world/inventory/BrewingStandMenu$FuelSlot 1/1
  Patching net/minecraft/world/inventory/BrewingStandMenu$IngredientsSlot 1/1
  Patching net/minecraft/world/inventory/BrewingStandMenu$PotionSlot 1/1
  Patching net/minecraft/world/inventory/BrewingStandMenu 1/1
  Patching net/minecraft/world/inventory/EnchantmentMenu$1 1/1
  Patching net/minecraft/world/inventory/EnchantmentMenu$2 1/1
  Patching net/minecraft/world/inventory/EnchantmentMenu$3 1/1
  Patching net/minecraft/world/inventory/EnchantmentMenu 1/1
  Patching net/minecraft/world/inventory/FurnaceResultSlot 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu$1 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu$2 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu$3 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu$4 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu 1/1
  Patching net/minecraft/world/inventory/InventoryMenu$1 1/1
  Patching net/minecraft/world/inventory/InventoryMenu$2 1/1
  Patching net/minecraft/world/inventory/InventoryMenu 1/1
  Patching net/minecraft/world/inventory/MenuType$MenuSupplier 1/1
  Patching net/minecraft/world/inventory/MenuType 1/1
  Patching net/minecraft/world/inventory/RecipeBookMenu 1/1
  Patching net/minecraft/world/inventory/RecipeBookType 1/1
  Patching net/minecraft/world/inventory/ResultSlot 1/1
  Patching net/minecraft/world/inventory/Slot 1/1
  Patching net/minecraft/world/item/ArmorItem$1 1/1
  Patching net/minecraft/world/item/ArmorItem 1/1
  Patching net/minecraft/world/item/ArrowItem 1/1
  Patching net/minecraft/world/item/AxeItem 1/1
  Patching net/minecraft/world/item/BannerItem 1/1
  Patching net/minecraft/world/item/BlockItem 1/1
  Patching net/minecraft/world/item/BoneMealItem 1/1
  Patching net/minecraft/world/item/BowItem 1/1
  Patching net/minecraft/world/item/BucketItem 1/1
  Patching net/minecraft/world/item/ChorusFruitItem 1/1
  Patching net/minecraft/world/item/CreativeModeTab$1 1/1
  Patching net/minecraft/world/item/CreativeModeTab$10 1/1
  Patching net/minecraft/world/item/CreativeModeTab$11 1/1
  Patching net/minecraft/world/item/CreativeModeTab$12 1/1
  Patching net/minecraft/world/item/CreativeModeTab$2 1/1
  Patching net/minecraft/world/item/CreativeModeTab$3 1/1
  Patching net/minecraft/world/item/CreativeModeTab$4 1/1
  Patching net/minecraft/world/item/CreativeModeTab$5 1/1
  Patching net/minecraft/world/item/CreativeModeTab$6 1/1
  Patching net/minecraft/world/item/CreativeModeTab$7 1/1
  Patching net/minecraft/world/item/CreativeModeTab$8 1/1
  Patching net/minecraft/world/item/CreativeModeTab$9 1/1
  Patching net/minecraft/world/item/CreativeModeTab 1/1
  Patching net/minecraft/world/item/CrossbowItem 1/1
  Patching net/minecraft/world/item/DiggerItem 1/1
  Patching net/minecraft/world/item/DyeColor 1/1
  Patching net/minecraft/world/item/DyeableHorseArmorItem 1/1
  Patching net/minecraft/world/item/ElytraItem 1/1
  Patching net/minecraft/world/item/EnchantedBookItem 1/1
  Patching net/minecraft/world/item/FishingRodItem 1/1
  Patching net/minecraft/world/item/HoeItem 1/1
  Patching net/minecraft/world/item/HorseArmorItem 1/1
  Patching net/minecraft/world/item/Item$1 1/1
  Patching net/minecraft/world/item/Item$Properties 1/1
  Patching net/minecraft/world/item/Item 1/1
  Patching net/minecraft/world/item/ItemStack$TooltipPart 1/1
  Patching net/minecraft/world/item/ItemStack 1/1
  Patching net/minecraft/world/item/Items 1/1
  Patching net/minecraft/world/item/MapItem 1/1
  Patching net/minecraft/world/item/MilkBucketItem 1/1
  Patching net/minecraft/world/item/MinecartItem$1 1/1
  Patching net/minecraft/world/item/MinecartItem 1/1
  Patching net/minecraft/world/item/MobBucketItem 1/1
  Patching net/minecraft/world/item/PickaxeItem 1/1
  Patching net/minecraft/world/item/PotionItem 1/1
  Patching net/minecraft/world/item/Rarity 1/1
  Patching net/minecraft/world/item/RecordItem 1/1
  Patching net/minecraft/world/item/ShearsItem 1/1
  Patching net/minecraft/world/item/ShieldItem 1/1
  Patching net/minecraft/world/item/ShovelItem 1/1
  Patching net/minecraft/world/item/SpawnEggItem 1/1
  Patching net/minecraft/world/item/StandingAndWallBlockItem 1/1
  Patching net/minecraft/world/item/SuspiciousStewItem 1/1
  Patching net/minecraft/world/item/SwordItem 1/1
  Patching net/minecraft/world/item/Tier 1/1
  Patching net/minecraft/world/item/Tiers 1/1
  Patching net/minecraft/world/item/TippedArrowItem 1/1
  Patching net/minecraft/world/item/alchemy/Potion 1/1
  Patching net/minecraft/world/item/alchemy/PotionBrewing$Mix 1/1
  Patching net/minecraft/world/item/alchemy/PotionBrewing 1/1
  Patching net/minecraft/world/item/crafting/BannerDuplicateRecipe 1/1
  Patching net/minecraft/world/item/crafting/BookCloningRecipe 1/1
  Patching net/minecraft/world/item/crafting/Ingredient$ItemValue 1/1
  Patching net/minecraft/world/item/crafting/Ingredient$TagValue 1/1
  Patching net/minecraft/world/item/crafting/Ingredient$Value 1/1
  Patching net/minecraft/world/item/crafting/Ingredient 1/1
  Patching net/minecraft/world/item/crafting/Recipe 1/1
  Patching net/minecraft/world/item/crafting/RecipeManager$1 1/1
  Patching net/minecraft/world/item/crafting/RecipeManager$CachedCheck 1/1
  Patching net/minecraft/world/item/crafting/RecipeManager 1/1
  Patching net/minecraft/world/item/crafting/RecipeSerializer 1/1
  Patching net/minecraft/world/item/crafting/RecipeType$1 1/1
  Patching net/minecraft/world/item/crafting/RecipeType 1/1
  Patching net/minecraft/world/item/crafting/RepairItemRecipe 1/1
  Patching net/minecraft/world/item/crafting/ShapedRecipe$Serializer 1/1
  Patching net/minecraft/world/item/crafting/ShapedRecipe 1/1
  Patching net/minecraft/world/item/crafting/ShapelessRecipe$Serializer 1/1
  Patching net/minecraft/world/item/crafting/ShapelessRecipe 1/1
  Patching net/minecraft/world/item/crafting/ShulkerBoxColoring 1/1
  Patching net/minecraft/world/item/crafting/SimpleCookingSerializer$CookieBaker 1/1
  Patching net/minecraft/world/item/crafting/SimpleCookingSerializer 1/1
  Patching net/minecraft/world/item/crafting/UpgradeRecipe$Serializer 1/1
  Patching net/minecraft/world/item/crafting/UpgradeRecipe 1/1
  Patching net/minecraft/world/item/enchantment/DiggingEnchantment 1/1
  Patching net/minecraft/world/item/enchantment/Enchantment$Rarity 1/1
  Patching net/minecraft/world/item/enchantment/Enchantment 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$1 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$10 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$11 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$12 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$13 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$14 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$2 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$3 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$4 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$5 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$6 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$7 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$8 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$9 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentHelper$EnchantmentVisitor 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentHelper 1/1
  Patching net/minecraft/world/item/enchantment/FrostWalkerEnchantment 1/1
  Patching net/minecraft/world/item/trading/MerchantOffer 1/1
  Patching net/minecraft/world/level/BaseSpawner 1/1
  Patching net/minecraft/world/level/BlockAndTintGetter 1/1
  Patching net/minecraft/world/level/BlockGetter 1/1
  Patching net/minecraft/world/level/ClipContext$Block 1/1
  Patching net/minecraft/world/level/ClipContext$Fluid 1/1
  Patching net/minecraft/world/level/ClipContext$ShapeGetter 1/1
  Patching net/minecraft/world/level/ClipContext 1/1
  Patching net/minecraft/world/level/DataPackConfig 1/1
  Patching net/minecraft/world/level/Explosion$BlockInteraction 1/1
  Patching net/minecraft/world/level/Explosion 1/1
  Patching net/minecraft/world/level/ExplosionDamageCalculator 1/1
  Patching net/minecraft/world/level/ForcedChunksSavedData 1/1
  Patching net/minecraft/world/level/Level$1 1/1
  Patching net/minecraft/world/level/Level 1/1
  Patching net/minecraft/world/level/LevelReader 1/1
  Patching net/minecraft/world/level/LevelSettings 1/1
  Patching net/minecraft/world/level/NaturalSpawner$1 1/1
  Patching net/minecraft/world/level/NaturalSpawner$AfterSpawnCallback 1/1
  Patching net/minecraft/world/level/NaturalSpawner$ChunkGetter 1/1
  Patching net/minecraft/world/level/NaturalSpawner$SpawnPredicate 1/1
  Patching net/minecraft/world/level/NaturalSpawner$SpawnState 1/1
  Patching net/minecraft/world/level/NaturalSpawner 1/1
  Patching net/minecraft/world/level/biome/Biome$1 1/1
  Patching net/minecraft/world/level/biome/Biome$BiomeBuilder 1/1
  Patching net/minecraft/world/level/biome/Biome$ClimateSettings 1/1
  Patching net/minecraft/world/level/biome/Biome$Precipitation 1/1
  Patching net/minecraft/world/level/biome/Biome$TemperatureModifier$1 1/1
  Patching net/minecraft/world/level/biome/Biome$TemperatureModifier$2 1/1
  Patching net/minecraft/world/level/biome/Biome$TemperatureModifier 1/1
  Patching net/minecraft/world/level/biome/Biome 1/1
  Patching net/minecraft/world/level/biome/BiomeGenerationSettings$Builder 1/1
  Patching net/minecraft/world/level/biome/BiomeGenerationSettings 1/1
  Patching net/minecraft/world/level/biome/BiomeSource 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$Builder 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier$1 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier$2 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier$3 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects 1/1
  Patching net/minecraft/world/level/biome/MobSpawnSettings$Builder 1/1
  Patching net/minecraft/world/level/biome/MobSpawnSettings$MobSpawnCost 1/1
  Patching net/minecraft/world/level/biome/MobSpawnSettings$SpawnerData 1/1
  Patching net/minecraft/world/level/biome/MobSpawnSettings 1/1
  Patching net/minecraft/world/level/block/BambooBlock 1/1
  Patching net/minecraft/world/level/block/BambooSaplingBlock 1/1
  Patching net/minecraft/world/level/block/BaseFireBlock 1/1
  Patching net/minecraft/world/level/block/BaseRailBlock$1 1/1
  Patching net/minecraft/world/level/block/BaseRailBlock 1/1
  Patching net/minecraft/world/level/block/BeehiveBlock 1/1
  Patching net/minecraft/world/level/block/Block$1 1/1
  Patching net/minecraft/world/level/block/Block$2 1/1
  Patching net/minecraft/world/level/block/Block$BlockStatePairKey 1/1
  Patching net/minecraft/world/level/block/Block 1/1
  Patching net/minecraft/world/level/block/Blocks 1/1
  Patching net/minecraft/world/level/block/BucketPickup 1/1
  Patching net/minecraft/world/level/block/BushBlock 1/1
  Patching net/minecraft/world/level/block/CactusBlock 1/1
  Patching net/minecraft/world/level/block/CampfireBlock 1/1
  Patching net/minecraft/world/level/block/ChestBlock$1 1/1
  Patching net/minecraft/world/level/block/ChestBlock$2$1 1/1
  Patching net/minecraft/world/level/block/ChestBlock$2 1/1
  Patching net/minecraft/world/level/block/ChestBlock$3 1/1
  Patching net/minecraft/world/level/block/ChestBlock$4 1/1
  Patching net/minecraft/world/level/block/ChestBlock 1/1
  Patching net/minecraft/world/level/block/ChorusFlowerBlock 1/1
  Patching net/minecraft/world/level/block/CocoaBlock$1 1/1
  Patching net/minecraft/world/level/block/CocoaBlock 1/1
  Patching net/minecraft/world/level/block/ComparatorBlock 1/1
  Patching net/minecraft/world/level/block/ConcretePowderBlock 1/1
  Patching net/minecraft/world/level/block/CoralBlock 1/1
  Patching net/minecraft/world/level/block/CropBlock 1/1
  Patching net/minecraft/world/level/block/DeadBushBlock 1/1
  Patching net/minecraft/world/level/block/DetectorRailBlock$1 1/1
  Patching net/minecraft/world/level/block/DetectorRailBlock 1/1
  Patching net/minecraft/world/level/block/DiodeBlock 1/1
  Patching net/minecraft/world/level/block/DoublePlantBlock 1/1
  Patching net/minecraft/world/level/block/DropExperienceBlock 1/1
  Patching net/minecraft/world/level/block/DropperBlock 1/1
  Patching net/minecraft/world/level/block/EnchantmentTableBlock 1/1
  Patching net/minecraft/world/level/block/FarmBlock 1/1
  Patching net/minecraft/world/level/block/FireBlock 1/1
  Patching net/minecraft/world/level/block/FlowerPotBlock 1/1
  Patching net/minecraft/world/level/block/GrowingPlantHeadBlock 1/1
  Patching net/minecraft/world/level/block/LeavesBlock 1/1
  Patching net/minecraft/world/level/block/LiquidBlock 1/1
  Patching net/minecraft/world/level/block/MagmaBlock 1/1
  Patching net/minecraft/world/level/block/MushroomBlock 1/1
  Patching net/minecraft/world/level/block/NetherWartBlock 1/1
  Patching net/minecraft/world/level/block/NoteBlock 1/1
  Patching net/minecraft/world/level/block/PowderSnowBlock 1/1
  Patching net/minecraft/world/level/block/PoweredRailBlock$1 1/1
  Patching net/minecraft/world/level/block/PoweredRailBlock 1/1
  Patching net/minecraft/world/level/block/PumpkinBlock 1/1
  Patching net/minecraft/world/level/block/RailBlock$1 1/1
  Patching net/minecraft/world/level/block/RailBlock 1/1
  Patching net/minecraft/world/level/block/RailState$1 1/1
  Patching net/minecraft/world/level/block/RailState 1/1
  Patching net/minecraft/world/level/block/RedStoneOreBlock 1/1
  Patching net/minecraft/world/level/block/RedStoneWireBlock$1 1/1
  Patching net/minecraft/world/level/block/RedStoneWireBlock 1/1
  Patching net/minecraft/world/level/block/SaplingBlock 1/1
  Patching net/minecraft/world/level/block/SculkCatalystBlock 1/1
  Patching net/minecraft/world/level/block/SculkSensorBlock 1/1
  Patching net/minecraft/world/level/block/SculkShriekerBlock 1/1
  Patching net/minecraft/world/level/block/SeagrassBlock 1/1
  Patching net/minecraft/world/level/block/SoundType 1/1
  Patching net/minecraft/world/level/block/SpawnerBlock 1/1
  Patching net/minecraft/world/level/block/SpongeBlock 1/1
  Patching net/minecraft/world/level/block/SpreadingSnowyDirtBlock 1/1
  Patching net/minecraft/world/level/block/StairBlock$1 1/1
  Patching net/minecraft/world/level/block/StairBlock 1/1
  Patching net/minecraft/world/level/block/StemBlock 1/1
  Patching net/minecraft/world/level/block/SugarCaneBlock 1/1
  Patching net/minecraft/world/level/block/SweetBerryBushBlock 1/1
  Patching net/minecraft/world/level/block/TallGrassBlock 1/1
  Patching net/minecraft/world/level/block/TntBlock 1/1
  Patching net/minecraft/world/level/block/TrapDoorBlock$1 1/1
  Patching net/minecraft/world/level/block/TrapDoorBlock 1/1
  Patching net/minecraft/world/level/block/TripWireBlock$1 1/1
  Patching net/minecraft/world/level/block/TripWireBlock 1/1
  Patching net/minecraft/world/level/block/TripWireHookBlock$1 1/1
  Patching net/minecraft/world/level/block/TripWireHookBlock 1/1
  Patching net/minecraft/world/level/block/TurtleEggBlock 1/1
  Patching net/minecraft/world/level/block/VineBlock$1 1/1
  Patching net/minecraft/world/level/block/VineBlock 1/1
  Patching net/minecraft/world/level/block/WebBlock 1/1
  Patching net/minecraft/world/level/block/entity/AbstractFurnaceBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/AbstractFurnaceBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/BaseContainerBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/BeaconBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/BeaconBlockEntity$BeaconBeamSection 1/1
  Patching net/minecraft/world/level/block/entity/BeaconBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/BlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/BrewingStandBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/BrewingStandBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/ChestBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/ChestBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/ConduitBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/HopperBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/ShulkerBoxBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/ShulkerBoxBlockEntity$AnimationStatus 1/1
  Patching net/minecraft/world/level/block/entity/ShulkerBoxBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/SpawnerBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/SpawnerBlockEntity 1/1
  Patching net/minecraft/world/level/block/piston/PistonBaseBlock$1 1/1
  Patching net/minecraft/world/level/block/piston/PistonBaseBlock 1/1
  Patching net/minecraft/world/level/block/piston/PistonMovingBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/piston/PistonMovingBlockEntity 1/1
  Patching net/minecraft/world/level/block/piston/PistonStructureResolver 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$1 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$BlockStateBase$Cache 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$BlockStateBase 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$OffsetType 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$Properties 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$StateArgumentPredicate 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$StatePredicate 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour 1/1
  Patching net/minecraft/world/level/block/state/BlockState 1/1
  Patching net/minecraft/world/level/block/state/properties/WoodType 1/1
  Patching net/minecraft/world/level/chunk/ChunkAccess$TicksToSave 1/1
  Patching net/minecraft/world/level/chunk/ChunkAccess 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$1 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$BoundTickingBlockEntity 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$EntityCreationType 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$PostLoadProcessor 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$RebindableTickingBlockEntityWrapper 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer$Configuration 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer$CountConsumer 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer$Data 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer$Strategy$1 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer$Strategy$2 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer$Strategy 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer 1/1
  Patching net/minecraft/world/level/chunk/ProtoChunk 1/1
  Patching net/minecraft/world/level/chunk/storage/ChunkSerializer 1/1
  Patching net/minecraft/world/level/chunk/storage/EntityStorage 1/1
  Patching net/minecraft/world/level/dimension/end/EndDragonFight 1/1
  Patching net/minecraft/world/level/entity/PersistentEntitySectionManager$Callback 1/1
  Patching net/minecraft/world/level/entity/PersistentEntitySectionManager$ChunkLoadStatus 1/1
  Patching net/minecraft/world/level/entity/PersistentEntitySectionManager 1/1
  Patching net/minecraft/world/level/entity/TransientEntitySectionManager$Callback 1/1
  Patching net/minecraft/world/level/entity/TransientEntitySectionManager 1/1
  Patching net/minecraft/world/level/levelgen/Beardifier$1 1/1
  Patching net/minecraft/world/level/levelgen/Beardifier$Rigid 1/1
  Patching net/minecraft/world/level/levelgen/Beardifier 1/1
  Patching net/minecraft/world/level/levelgen/DebugLevelSource 1/1
  Patching net/minecraft/world/level/levelgen/PatrolSpawner 1/1
  Patching net/minecraft/world/level/levelgen/PhantomSpawner 1/1
  Patching net/minecraft/world/level/levelgen/feature/Feature 1/1
  Patching net/minecraft/world/level/levelgen/feature/MonsterRoomFeature 1/1
  Patching net/minecraft/world/level/levelgen/feature/configurations/TreeConfiguration$TreeConfigurationBuilder 1/1
  Patching net/minecraft/world/level/levelgen/feature/configurations/TreeConfiguration 1/1
  Patching net/minecraft/world/level/levelgen/structure/Structure$GenerationContext 1/1
  Patching net/minecraft/world/level/levelgen/structure/Structure$GenerationStub 1/1
  Patching net/minecraft/world/level/levelgen/structure/Structure$StructureSettings 1/1
  Patching net/minecraft/world/level/levelgen/structure/Structure 1/1
  Patching net/minecraft/world/level/levelgen/structure/StructurePiece$1 1/1
  Patching net/minecraft/world/level/levelgen/structure/StructurePiece$BlockSelector 1/1
  Patching net/minecraft/world/level/levelgen/structure/StructurePiece 1/1
  Patching net/minecraft/world/level/levelgen/structure/StructureStart 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureProcessor 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$1 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$Palette 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$SimplePalette 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$StructureBlockInfo 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$StructureEntityInfo 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate 1/1
  Patching net/minecraft/world/level/lighting/BlockLightEngine 1/1
  Patching net/minecraft/world/level/lighting/DynamicGraphMinFixedPoint$1 1/1
  Patching net/minecraft/world/level/lighting/DynamicGraphMinFixedPoint$2 1/1
  Patching net/minecraft/world/level/lighting/DynamicGraphMinFixedPoint 1/1
  Patching net/minecraft/world/level/lighting/LayerLightEngine 1/1
  Patching net/minecraft/world/level/lighting/SkyLightEngine 1/1
  Patching net/minecraft/world/level/material/FlowingFluid$1 1/1
  Patching net/minecraft/world/level/material/FlowingFluid 1/1
  Patching net/minecraft/world/level/material/Fluid 1/1
  Patching net/minecraft/world/level/material/FluidState 1/1
  Patching net/minecraft/world/level/material/LavaFluid$Flowing 1/1
  Patching net/minecraft/world/level/material/LavaFluid$Source 1/1
  Patching net/minecraft/world/level/material/LavaFluid 1/1
  Patching net/minecraft/world/level/pathfinder/AmphibiousNodeEvaluator 1/1
  Patching net/minecraft/world/level/pathfinder/BlockPathTypes 1/1
  Patching net/minecraft/world/level/pathfinder/WalkNodeEvaluator 1/1
  Patching net/minecraft/world/level/portal/PortalForcer 1/1
  Patching net/minecraft/world/level/portal/PortalShape 1/1
  Patching net/minecraft/world/level/saveddata/maps/MapDecoration$Type 1/1
  Patching net/minecraft/world/level/saveddata/maps/MapDecoration 1/1
  Patching net/minecraft/world/level/storage/DimensionDataStorage 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelCandidates 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelDirectory 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelStorageAccess$1 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelStorageAccess$2 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelStorageAccess 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource 1/1
  Patching net/minecraft/world/level/storage/LevelSummary$BackupStatus 1/1
  Patching net/minecraft/world/level/storage/LevelSummary 1/1
  Patching net/minecraft/world/level/storage/PlayerDataStorage 1/1
  Patching net/minecraft/world/level/storage/PrimaryLevelData 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext$DynamicDrop 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext$EntityTarget$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext$EntityTarget 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext 1/1
  Patching net/minecraft/world/level/storage/loot/LootPool$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/LootPool$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/LootPool 1/1
  Patching net/minecraft/world/level/storage/loot/LootTable$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/LootTable$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/LootTable 1/1
  Patching net/minecraft/world/level/storage/loot/LootTables 1/1
  Patching net/minecraft/world/level/storage/loot/functions/LootingEnchantFunction$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/functions/LootingEnchantFunction$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/functions/LootingEnchantFunction 1/1
  Patching net/minecraft/world/level/storage/loot/functions/SmeltItemFunction$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/functions/SmeltItemFunction 1/1
  Patching net/minecraft/world/level/storage/loot/parameters/LootContextParamSets 1/1
  Patching net/minecraft/world/level/storage/loot/predicates/LootItemRandomChanceWithLootingCondition$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/predicates/LootItemRandomChanceWithLootingCondition 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider$1 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider$2 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider$Getter 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider$InlineSerializer 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider 1/1
  Patching net/minecraft/world/item/crafting/RecipeType$2 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier$ColorModifier 1/1
  Patching net/minecraft/Util$3 1/1
  Patching net/minecraft/Util$4 1/1
  Patching net/minecraft/world/item/Items$1 1/1

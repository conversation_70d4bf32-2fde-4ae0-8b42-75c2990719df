package com.yx.ikun;

import com.mojang.logging.LogUtils;
import com.yx.ikun.block.ModBlock;
import com.yx.ikun.item.ModItem;
import com.yx.ikun.painting.ModPaintings;
import com.yx.ikun.sound.ModSound;
import com.yx.ikun.world.feature.ModConfiguredFeatures;
import com.yx.ikun.world.feature.ModPlaceFeature;
import net.minecraft.client.renderer.ItemBlockRenderTypes;
import net.minecraft.client.renderer.RenderType;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.slf4j.Logger;

@Mod(Ikun.MODID)
public class Ikun {

    public static final String MODID = "ikun";
    private static final Logger LOGGER = LogUtils.getLogger();

    public Ikun() {
        IEventBus modEventBus = FMLJavaModLoadingContext.get().getModEventBus();
        ModItem.register(modEventBus);
        ModSound.register(modEventBus);
        ModBlock.register(modEventBus);
        ModConfiguredFeatures.register(modEventBus);
        ModPlaceFeature.register(modEventBus);
        ModPaintings.register(modEventBus);
        modEventBus.addListener(this::commonSetup);
        MinecraftForge.EVENT_BUS.register(this);
    }

    private void commonSetup(final FMLCommonSetupEvent event) {
    }

    @Mod.EventBusSubscriber(modid = Ikun.MODID, value = Dist.CLIENT, bus = Mod.EventBusSubscriber.Bus.MOD)
    public class ClientSetup {

        @SubscribeEvent
        public static void onClientSetup(FMLClientSetupEvent event) {

            ItemBlockRenderTypes.setRenderLayer(ModBlock.WINTER_WINDOW.get(), RenderType.translucent());
        }
    }
}
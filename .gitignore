# Minecraft Development Kit
.mdk/

# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

# Gradle
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# Gradle Wrapper
!gradle-wrapper.properties

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# Mac
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Minecraft
run/
run-data/
logs/
crash-reports/
screenshots/
saves/
resourcepacks/
shaderpacks/
config/
mods/
*.txt
!README.txt

# Minecraft Development
src/generated/
.mcreator/

# Forge specific
/eclipse/
/build/
/run/
/out/

# Fabric specific
remappedSrc/

# Common IDEs
nbproject/
.vscode/
.settings/
.metadata/
.recommenders/

# OS generated files
.DS_Store?
._*
.Spotlight-V100
.Trashes
Icon?
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Archives
*.7z
*.dmg
*.gz
*.iso
*.rar
*.tar
*.zip

# Logs and databases
*.log
*.sql
*.sqlite

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Minecraft Forge MDK specific
/eclipse/
/build/
/run/
/out/
/logs/
/crash-reports/
/screenshots/
/saves/
/resourcepacks/
/config/
/mods/
/defaultconfigs/
usernamecache.json
usercache.json
options.txt

# Exclude important files that should be kept
!src/
!gradle/
!gradlew
!gradlew.bat
!build.gradle
!settings.gradle
!gradle.properties
!README.md
!LICENSE
!.gitignore
